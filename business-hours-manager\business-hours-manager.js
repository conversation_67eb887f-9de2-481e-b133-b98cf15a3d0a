/**
 * Business Hours Manager
 * Handles the creation and management of business hours and scanner hours UI components
 * Migrated from BusinessDetailsForm class for better modularity
 * Exposes custom events for external integration and data access
 */
class BusinessHoursManager {
    constructor(element = document) {
        this.element = element; // Element to dispatch events on
        this.initializeConstants();
        this.timeConfigInitialization();
    }

    /**
     * Initialize constants and configuration
     */
    initializeConstants() {
        // Days configuration
        this.DAYS_CONFIG = {
            KEYS: ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'],
            NAMES: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
            WEEKDAYS: ['mon', 'tue', 'wed', 'thu', 'fri', 'sat'],
            WEEKEND: ['sun'],
            DAY_MAP: {
                'Sunday': 'sun',
                'Monday': 'mon',
                'Tuesday': 'tue',
                'Wednesday': 'wed',
                'Thursday': 'thu',
                'Friday': 'fri',
                'Saturday': 'sat'
            }
        };

        // Hours configuration
        this.HOURS_CONFIG = {
            PREFIXES: ['biz', 'scan'],
            TYPES: ['business', 'scanner']
        };

        // LocalStorage keys
        this.STORAGE_KEYS = {
            FORM_DATA: 'onboardingFormData',
            CURRENT_STEP: 'onboardingCurrentStep',
            SAME_HOURS_CHECKED: 'sameHoursChecked'
        };
    }

    /**
     * Global Time Configuration - Can be loaded from database
     * For simplicity, it's hardcoded here 
     */
    timeConfigInitialization() {
        this.timeConfig = {
            options: [
                { value: '06:00', label: '06:00 AM' },
                { value: '06:30', label: '06:30 AM' },
                { value: '07:00', label: '07:00 AM' },
                { value: '07:30', label: '07:30 AM' },
                { value: '08:00', label: '08:00 AM' },
                { value: '08:30', label: '08:30 AM' },
                { value: '09:00', label: '09:00 AM' },
                { value: '09:30', label: '09:30 AM' },
                { value: '10:00', label: '10:00 AM' },
                { value: '10:30', label: '10:30 AM' },
                { value: '11:00', label: '11:00 AM' },
                { value: '11:30', label: '11:30 AM' },
                { value: '12:00', label: '12:00 PM' },
                { value: '12:30', label: '12:30 PM' },
                { value: '13:00', label: '01:00 PM' },
                { value: '13:30', label: '01:30 PM' },
                { value: '14:00', label: '02:00 PM' },
                { value: '14:30', label: '02:30 PM' },
                { value: '15:00', label: '03:00 PM' },
                { value: '15:30', label: '03:30 PM' },
                { value: '16:00', label: '04:00 PM' },
                { value: '16:30', label: '04:30 PM' },
                { value: '17:00', label: '05:00 PM' },
                { value: '17:30', label: '05:30 PM' },
                { value: '18:00', label: '06:00 PM' },
                { value: '18:30', label: '06:30 PM' },
                { value: '19:00', label: '07:00 PM' },
                { value: '19:30', label: '07:30 PM' },
                { value: '20:00', label: '08:00 PM' },
                { value: '20:30', label: '08:30 PM' },
                { value: '21:00', label: '09:00 PM' },
                { value: '21:30', label: '09:30 PM' },
                { value: '22:00', label: '10:00 PM' },
                { value: '22:30', label: '10:30 PM' },
                { value: '23:00', label: '11:00 PM' },
                { value: '23:30', label: '11:30 PM' }
            ]
        };
    }

    /**
     * Generate time options HTML from configuration
     * @param {Array} options - Array of time option objects with 'value' and 'label'
     * @param {String} selectedValue - Value to be selected by default
     */
    generateTimeOptionsHTML(options, selectedValue = null) {
        return options.map(option => {
            const isSelected = selectedValue ?
                option.value === selectedValue :
                (option.selected || false);
            return `<option value="${option.value}" ${isSelected ? 'selected' : ''}>${option.label}</option>`;
        }).join('');
    }

    /**
     * Initialize Facility Info Step
     * Sets up time options and "same as business hours" functionality
     */
    initializeFacilityInfoStep() {
        // Generate dynamic hours sections
        this.generateHoursSection('business');
        this.generateHoursSection('scanner');

        // Set up event listeners after generating sections
        this.setupHoursEventListeners();

        // Same as Business Hours toggle
        const sameHoursCheck = document.getElementById('same-hours');
        if (sameHoursCheck) {
            // Check if scanner hours should be auto-checked (same as business hours) by default
            const sameHoursWasChecked = localStorage.getItem(this.STORAGE_KEYS.SAME_HOURS_CHECKED) === 'true';

            // Auto-check the "same as business hours" checkbox by default for scanner hours
            if (!sameHoursWasChecked && !sameHoursCheck.checked) {
                sameHoursCheck.checked = true;
                localStorage.setItem(this.STORAGE_KEYS.SAME_HOURS_CHECKED, 'true');

                // Initially hide scanner hours section and copy business hours
                const scannerHoursSection = document.getElementById('scanner-hours');
                const scannerHoursContainer = document.querySelector('.scanner-hours-container');
                this.hideScannerHours(scannerHoursSection, scannerHoursContainer);

                // Copy business hours to scanner hours
                setTimeout(() => {
                    this.copyBusinessHoursToScanner();
                }, 200);
            } else if (sameHoursCheck.checked) {
                // If already checked, ensure the UI is in the correct state
                const scannerHoursSection = document.getElementById('scanner-hours');
                const scannerHoursContainer = document.querySelector('.scanner-hours-container');
                this.hideScannerHours(scannerHoursSection, scannerHoursContainer);

                setTimeout(() => {
                    this.copyBusinessHoursToScanner();
                }, 200);
            }

            sameHoursCheck.addEventListener('change', () => {
                const scannerHoursSection = document.getElementById('scanner-hours');
                const scannerHoursContainer = document.querySelector('.scanner-hours-container');

                if (sameHoursCheck.checked) {
                    localStorage.setItem(this.STORAGE_KEYS.SAME_HOURS_CHECKED, 'true');
                    // Hide scanner hours section with animation
                    this.hideScannerHours(scannerHoursSection, scannerHoursContainer);

                    // Copy business hours to scanner hours (for form submission)
                    setTimeout(() => {
                        this.copyBusinessHoursToScanner();
                    }, 200); // Delay to allow animation to start
                } else {
                    localStorage.removeItem(this.STORAGE_KEYS.SAME_HOURS_CHECKED);
                    // Show scanner hours section with animation
                    this.showScannerHours(scannerHoursSection, scannerHoursContainer);

                    // reset to default state
                    setTimeout(() => {
                        this.generateHoursSection('scanner');
                        this.setupHoursEventListeners();
                        this.saveCurrentData();
                    }, 300);
                }
            });
        }
    }

    /**
     * Generate dynamic hours section
     * @param {string} type - Either 'business' or 'scanner'
     */
    generateHoursSection(type) {
        const days = this.DAYS_CONFIG.KEYS;
        const prefix = type === 'business' ? 'biz' : 'scan';
        const containerId = type === 'business' ? 'business-hours' : 'scanner-hours';

        const container = document.getElementById(containerId);
        if (!container) return;

        // Clear existing content
        container.innerHTML = '';

        // Generate each day row
        days.forEach(day => {
            const label = day.charAt(0).toUpperCase() + day.slice(1); // sun → Sun
            const dayRow = this.createDayRow(prefix, day, label, type);
            container.appendChild(dayRow);
        });
    }

    /**
     * Create a single day row
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day key (e.g., 'mon', 'tue')
     * @param {string} label - Display label for the day
     * @param {string} type - Either 'business' or 'scanner'
     */
    createDayRow(prefix, day, label, type) {
        const row = document.createElement('div');
        row.className = 'bd-hours-row';

        // Default checked state (Monday-Friday for business, Monday-Friday for scanner)
        const isWeekday = this.DAYS_CONFIG.WEEKDAYS.includes(day);
        const isChecked = isWeekday;

        row.innerHTML = `
            <div class="toggle-switch-container">
                <div class="toggle-switch">
                    <input
                        type="checkbox"
                        id="${prefix}-${day}"
                        name="${prefix}-${day}"
                        class="day-toggle"
                        data-day="${day}"
                        ${isChecked ? 'checked' : ''}
                    />
                    <label for="${prefix}-${day}" class="toggle-slider"></label>
                </div>
                <label for="${prefix}-${day}">${label}</label>
            </div>
            <div class="time-inputs-container">
                <div class="time-inputs-default">
                    ${isChecked ? this.createTimeInputsHTML(prefix, day, type) : ''}
                    ${!isChecked ? '<span class="unavailable-text">Unavailable</span>' : ''}
                    ${isChecked ? `<button class="add-hours-btn plus-icon" type="button"></button>` : ''}
                </div>
            </div>
        `;

        return row;
    }

    /**
     * Create time inputs HTML
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day key
     * @param {string} type - Either 'business' or 'scanner'
     */
    createTimeInputsHTML(prefix, day, type) {
        const startOptions = this.getTimeOptions(type, 'start');
        const endOptions = this.getTimeOptions(type, 'end');

        return `
            <div class="time-inputs">
                <select id="${prefix}-${day}-start" name="${prefix}-${day}-start" class="time-select">
                    ${startOptions}
                </select>
                <span>-</span>
                <select id="${prefix}-${day}-end" name="${prefix}-${day}-end" class="time-select">
                    ${endOptions}
                </select>
            </div>
        `;
    }

    /**
     * Get time options based on type
     * @param {string} type - Either 'business' or 'scanner'
     * @param {string} period - Either 'start' or 'end'
     */
    getTimeOptions(type, period) {
        // Use single global time configuration for all time options
        return this.generateTimeOptionsHTML(this.timeConfig.options);
    }

    /**
     * Setup event listeners for hours sections
     */
    setupHoursEventListeners() {
        // Toggle day availability for business hours and scanner hours
        const dayToggles = document.querySelectorAll('.day-toggle');
        dayToggles.forEach(toggle => {
            // Check if event listener already exists
            if (!toggle.hasAttribute('data-listener-added')) {
                toggle.addEventListener('change', () => {
                    this.toggleDayAvailability(toggle);
                    // Auto-save data when toggle is changed
                    this.saveCurrentData();
                });
                toggle.setAttribute('data-listener-added', 'true');
            }

            // Initialize toggle state on load
            this.toggleDayAvailability(toggle);
        });

        // Add time slot buttons - only add listeners to buttons that don't have them
        const addHoursBtns = document.querySelectorAll('.add-hours-btn:not([data-listener-added])');
        addHoursBtns.forEach(btn => {
            btn.addEventListener('click', this.addTimeSlot.bind(this));
            btn.setAttribute('data-listener-added', 'true');
        });

        // Add auto-save functionality to all existing time select dropdowns
        const timeSelects = document.querySelectorAll('.time-select:not([data-listener-added])');
        timeSelects.forEach(select => {
            select.addEventListener('change', () => {
                // If start time changed, update corresponding end time options
                const startMatch = select.id.match(/^(.+)-start(-\d*)?$/);
                if (startMatch) {
                    const base = startMatch[1];
                    const suffix = startMatch[2] || '';
                    const endSelect = document.getElementById(`${base}-end${suffix}`);
                    if (endSelect) {
                        // Filter options to be greater than selected start value
                        const filteredOptions = this.timeConfig.options.filter(opt => opt.value > select.value);
                        endSelect.innerHTML = this.generateTimeOptionsHTML(filteredOptions);
                    }
                }
                this.saveCurrentData();
            });
            select.setAttribute('data-listener-added', 'true');
        });
        // Initialize end time options based on current start values
        timeSelects.forEach(select => {
            if (select.id.match(/^(.+)-start(-\d*)?$/)) {
                select.dispatchEvent(new Event('change'));
            }
        });
    }

    /**
     * Toggle day availability
     * @param {HTMLInputElement} toggle - The day toggle checkbox
     */
    toggleDayAvailability(toggle) {
        const row = toggle.closest('.bd-hours-row');
        const timeInputsContainer = row.querySelector('.time-inputs-container');
        const timeInputsDefault = timeInputsContainer.querySelector('.time-inputs-default');
        const timeInputs = timeInputsDefault.querySelector('.time-inputs');
        const addBtn = timeInputsDefault.querySelector('.add-hours-btn.plus-icon');
        const unavailableText = timeInputsDefault.querySelector('.unavailable-text');
        const additionalTimeSlots = timeInputsContainer.querySelectorAll('.additional-time-slot');

        if (toggle.checked) {
            // Day is available
            if (!timeInputs) {
                // Create time inputs if they don't exist
                this.createTimeInputs(row, toggle);
            } else {
                // Show existing time inputs and add button
                timeInputs.style.display = 'flex';
                if (addBtn) addBtn.style.display = 'flex';
            }

            // Hide unavailable text
            if (unavailableText) {
                unavailableText.style.display = 'none';
            }
        } else {
            // Day is unavailable
            if (timeInputs) {
                timeInputs.style.display = 'none';
            }
            if (addBtn) {
                addBtn.style.display = 'none';
            }

            // Remove all additional time slots
            additionalTimeSlots.forEach(slot => {
                slot.remove();
            });

            // Show unavailable text
            if (unavailableText) {
                unavailableText.style.display = 'inline';
            } else {
                // Create unavailable text if it doesn't exist
                const newUnavailableText = document.createElement('span');
                newUnavailableText.className = 'unavailable-text';
                newUnavailableText.textContent = 'Unavailable';
                timeInputsDefault.appendChild(newUnavailableText);
            }
        }

        // Dispatch specific event based on the type (business or scanner)
        const prefix = toggle.id.split('-')[0]; // 'biz' or 'scan'
        if (prefix === 'biz') {
            this.dispatchBusinessHoursChangeEvent();
        } else if (prefix === 'scan') {
            this.dispatchScannerHoursChangeEvent();
        }
    }

    /**
     * Create time inputs for a day
     * @param {HTMLElement} row - The day row element
     * @param {HTMLInputElement} toggle - The day toggle checkbox
     */
    createTimeInputs(row, toggle) {
        const prefix = toggle.id.split('-')[0]; // 'biz' or 'scan'
        const day = toggle.id.split('-')[1]; // day name
        const timeInputsContainer = row.querySelector('.time-inputs-container');
        const timeInputsDefault = timeInputsContainer.querySelector('.time-inputs-default');

        // Remove unavailable text if it exists
        const unavailableText = timeInputsDefault.querySelector('.unavailable-text');
        if (unavailableText) {
            unavailableText.remove();
        }

        const newTimeInputs = document.createElement('div');
        newTimeInputs.className = 'time-inputs';

        // Generate options using global configuration based on type
        const type = prefix === 'biz' ? 'business' : 'scanner';
        const startOptions = this.getTimeOptions(type, 'start');
        const endOptions = this.getTimeOptions(type, 'end');

        newTimeInputs.innerHTML = `
            <select id="${prefix}-${day}-start" name="${prefix}-${day}-start" class="time-select">
                ${startOptions}
            </select>
            <span>-</span>
            <select id="${prefix}-${day}-end" name="${prefix}-${day}-end" class="time-select">
                ${endOptions}
            </select>
        `;

        // Add to time-inputs-default container
        timeInputsDefault.appendChild(newTimeInputs);

        // Create add button
        const newAddBtn = document.createElement('button');
        newAddBtn.className = 'add-hours-btn plus-icon';
        newAddBtn.type = 'button';

        newAddBtn.addEventListener('click', this.addTimeSlot.bind(this));
        newAddBtn.setAttribute('data-listener-added', 'true');
        timeInputsDefault.appendChild(newAddBtn);
    }

    /**
     * Add time slot functionality
     * @param {Event} e - The click event
     */
    addTimeSlot(e) {
        const row = e.target.closest('.bd-hours-row');
        const timeInputsContainer = row.querySelector('.time-inputs-container');

        // Get prefix and day from the existing toggle
        const toggle = row.querySelector('.day-toggle');
        const prefix = toggle.id.split('-')[0]; // 'biz' or 'scan'
        const day = toggle.id.split('-')[1]; // day name

        // Generate unique index for additional time slots
        const existingSlots = timeInputsContainer.querySelectorAll('.additional-time-slot');
        const slotIndex = existingSlots.length + 1;

        // Create a new time slot div
        const newTimeSlot = document.createElement('div');
        newTimeSlot.className = 'additional-time-slot';

        // Create time inputs with proper IDs and names using global configuration
        const timeInputs = document.createElement('div');
        timeInputs.className = 'time-inputs';

        // Generate time options using global configuration
        const startOptions = this.generateTimeOptionsWithSelected('12:00');
        const endOptions = this.generateTimeOptionsWithSelected('18:00');

        timeInputs.innerHTML = `
            <select id="${prefix}-${day}-start-${slotIndex}" name="${prefix}-${day}-start-${slotIndex}" class="time-select">
                ${startOptions}
            </select>
            <span>-</span>
            <select id="${prefix}-${day}-end-${slotIndex}" name="${prefix}-${day}-end-${slotIndex}" class="time-select">
                ${endOptions}
            </select>
        `;

        // Create remove button
        const removeBtn = document.createElement('button');
        removeBtn.className = 'add-hours-btn remove-hours-btn';
        removeBtn.type = 'button';
        removeBtn.style.backgroundColor = "#EBEBEB";
        removeBtn.setAttribute('aria-label', 'Remove time slot');

        removeBtn.addEventListener('click', () => {
            this.removeTimeSlot(newTimeSlot);
            // Auto-save data after removing time slot
            setTimeout(() => {
                this.saveCurrentData();
            }, 350); // Slight delay to ensure removal animation completes
        });

        // Add time inputs and remove button to the new time slot
        newTimeSlot.appendChild(timeInputs);
        newTimeSlot.appendChild(removeBtn);

        // Set initial state for animation
        newTimeSlot.style.opacity = '0';
        newTimeSlot.style.transform = 'translateY(-10px) scale(0.95)';
        newTimeSlot.style.maxHeight = '0px';
        newTimeSlot.style.overflow = 'hidden';
        newTimeSlot.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

        // Add the additional time slot at the top (right after time-inputs-default)
        const timeInputsDefault = timeInputsContainer.querySelector('.time-inputs-default');
        if (timeInputsDefault && timeInputsDefault.nextSibling) {
            timeInputsContainer.insertBefore(newTimeSlot, timeInputsDefault.nextSibling);
        } else {
            timeInputsContainer.appendChild(newTimeSlot);
        }

        // Trigger animation on next frame
        requestAnimationFrame(() => {
            newTimeSlot.style.opacity = '1';
            newTimeSlot.style.transform = 'translateY(0) scale(1)';
            newTimeSlot.style.maxHeight = '60px'; // Adjust based on actual content height

            // Remove maxHeight after animation completes
            setTimeout(() => {
                newTimeSlot.style.maxHeight = '';
                newTimeSlot.style.overflow = '';

                // Add auto-save event listeners to the new time select dropdowns
                const timeSelects = newTimeSlot.querySelectorAll('.time-select');
                timeSelects.forEach(select => {
                    select.addEventListener('change', () => {
                        this.saveCurrentData();
                    });
                    select.setAttribute('data-listener-added', 'true');
                });

                // Auto-save data after adding time slot
                this.saveCurrentData();
            }, 300);
        });
    }

    /**
     * Remove time slot functionality with smooth animation
     * @param {HTMLElement} timeSlotElement - The time slot element to remove
     */
    removeTimeSlot(timeSlotElement) {
        if (timeSlotElement && timeSlotElement.parentNode) {
            // Set up animation styles
            timeSlotElement.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            timeSlotElement.style.transformOrigin = 'center';

            // Get current height for smooth collapse animation
            const currentHeight = timeSlotElement.offsetHeight;
            timeSlotElement.style.height = currentHeight + 'px';

            // Force a reflow to ensure the height is set
            timeSlotElement.offsetHeight;

            // Start the removal animation
            timeSlotElement.style.opacity = '0';
            timeSlotElement.style.transform = 'translateY(-10px) scale(0.95)';
            timeSlotElement.style.height = '0px';
            timeSlotElement.style.marginBottom = '0px';
            timeSlotElement.style.paddingTop = '0px';
            timeSlotElement.style.paddingBottom = '0px';

            // Remove element after animation completes
            setTimeout(() => {
                if (timeSlotElement && timeSlotElement.parentNode) {
                    timeSlotElement.parentNode.removeChild(timeSlotElement);
                }
            }, 300);
        }
    }

    /**
     * Hide scanner hours with smooth animation
     * @param {HTMLElement} scannerHoursSection - Scanner hours section element
     * @param {HTMLElement} scannerHoursContainer - Scanner hours container element
     */
    hideScannerHours(scannerHoursSection, scannerHoursContainer) {
        if (scannerHoursSection) {
            scannerHoursSection.classList.add('hiding');
            scannerHoursSection.classList.remove('showing');
        }

        if (scannerHoursContainer) {
            scannerHoursContainer.classList.add('sync-enabled');
            scannerHoursContainer.querySelector('label').classList.add('checked');
        }
    }

    /**
     * Show scanner hours with smooth animation
     * @param {HTMLElement} scannerHoursSection - Scanner hours section element
     * @param {HTMLElement} scannerHoursContainer - Scanner hours container element
     */
    showScannerHours(scannerHoursSection, scannerHoursContainer) {
        if (scannerHoursContainer) {
            scannerHoursContainer.classList.remove('sync-enabled');
            scannerHoursContainer.querySelector('label').classList.remove('checked');
        }

        if (scannerHoursSection) {
            scannerHoursSection.classList.remove('hiding');
            scannerHoursSection.classList.add('showing');
        }
    }

    /**
     * Copy business hours to scanner hours
     */
    copyBusinessHoursToScanner() {
        const bizDayToggles = document.querySelectorAll('[id^="biz-"].day-toggle');

        bizDayToggles.forEach(toggle => {
            const day = toggle.getAttribute('data-day');
            if (day) {
                const scanToggle = document.querySelector(`[id^="scan-"][data-day="${day}"]`);
                if (scanToggle) {
                    // Set scanner toggle to match business toggle
                    scanToggle.checked = toggle.checked;

                    // Update UI
                    const scanRow = scanToggle.closest('.bd-hours-row');
                    if (scanRow) {
                        const scanTimeInputsContainer = scanRow.querySelector('.time-inputs-container');
                        const scanTimeInputsDefault = scanTimeInputsContainer.querySelector('.time-inputs-default');
                        const scanTimeInputs = scanTimeInputsDefault.querySelector('.time-inputs');
                        const scanAddBtn = scanTimeInputsDefault.querySelector('.add-hours-btn.plus-icon');
                        const scanUnavailableText = scanTimeInputsDefault.querySelector('.unavailable-text');
                        const scanAdditionalSlots = scanTimeInputsContainer.querySelectorAll('.additional-time-slot');

                        if (toggle.checked) {
                            // Day is available

                            // Remove existing additional time slots first
                            scanAdditionalSlots.forEach(slot => slot.remove());

                            if (!scanTimeInputs) {
                                // Create time inputs if they don't exist
                                this.createTimeInputs(scanRow, scanToggle);
                            } else {
                                // Show existing time inputs and add button
                                scanTimeInputs.style.display = 'flex';
                                if (scanAddBtn) scanAddBtn.style.display = 'flex';
                                if (scanUnavailableText) scanUnavailableText.style.display = 'none';
                            }

                            // Copy time values if both have time inputs
                            const bizRow = toggle.closest('.bd-hours-row');
                            const bizTimeInputsContainer = bizRow.querySelector('.time-inputs-container');
                            const bizTimeInputsDefault = bizTimeInputsContainer.querySelector('.time-inputs-default');
                            const bizTimeInputs = bizTimeInputsDefault.querySelector('.time-inputs');

                            if (bizTimeInputs && scanTimeInputs) {
                                const bizSelects = bizTimeInputs.querySelectorAll('select');
                                const scanSelects = scanTimeInputs.querySelectorAll('select');

                                if (bizSelects.length >= 2 && scanSelects.length >= 2) {
                                    scanSelects[0].value = bizSelects[0].value;
                                    scanSelects[1].value = bizSelects[1].value;
                                }
                            }

                            // Copy additional time slots from business hours
                            const bizAdditionalSlots = bizTimeInputsContainer.querySelectorAll('.additional-time-slot');
                            bizAdditionalSlots.forEach((bizSlot, index) => {
                                const bizSlotSelects = bizSlot.querySelectorAll('select');
                                if (bizSlotSelects.length >= 2) {
                                    // Get the actual values from business hours
                                    const startValue = bizSlotSelects[0].value;
                                    const endValue = bizSlotSelects[1].value;

                                    // Create corresponding scanner slot with proper values
                                    const newScanSlot = document.createElement('div');
                                    newScanSlot.className = 'additional-time-slot';

                                    const scanSlotTimeInputs = document.createElement('div');
                                    scanSlotTimeInputs.className = 'time-inputs';

                                    // Generate proper time options with selected values
                                    const startOptions = this.generateTimeOptionsWithSelected(startValue);
                                    const endOptions = this.generateTimeOptionsWithSelected(endValue);

                                    scanSlotTimeInputs.innerHTML = `
                                        <select id="scan-${day}-start-${index + 1}" name="scan-${day}-start-${index + 1}" class="time-select">
                                            ${startOptions}
                                        </select>
                                        <span>-</span>
                                        <select id="scan-${day}-end-${index + 1}" name="scan-${day}-end-${index + 1}" class="time-select">
                                            ${endOptions}
                                        </select>
                                    `;

                                    const scanRemoveBtn = document.createElement('button');
                                    scanRemoveBtn.className = 'add-hours-btn remove-hours-btn';
                                    scanRemoveBtn.type = 'button';
                                    scanRemoveBtn.style.backgroundColor = "#EBEBEB";
                                    scanRemoveBtn.setAttribute('aria-label', 'Remove time slot');

                                    scanRemoveBtn.addEventListener('click', () => {
                                        this.removeTimeSlot(newScanSlot);
                                    });

                                    newScanSlot.appendChild(scanSlotTimeInputs);
                                    newScanSlot.appendChild(scanRemoveBtn);

                                    // Set up transition for future animations
                                    newScanSlot.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

                                    // Insert at the top (right after time-inputs-default) to match our insertion order
                                    if (scanTimeInputsDefault && scanTimeInputsDefault.nextSibling) {
                                        scanTimeInputsContainer.insertBefore(newScanSlot, scanTimeInputsDefault.nextSibling);
                                    } else {
                                        scanTimeInputsContainer.appendChild(newScanSlot);
                                    }
                                }
                            });

                            // Update the UI to ensure everything is properly displayed
                            this.toggleDayAvailability(scanToggle);
                        } else {
                            // Day is unavailable
                            if (scanTimeInputs) scanTimeInputs.style.display = 'none';
                            if (scanAddBtn) scanAddBtn.style.display = 'none';

                            // Remove all additional time slots
                            scanAdditionalSlots.forEach(slot => slot.remove());

                            if (scanUnavailableText) {
                                scanUnavailableText.style.display = 'inline';
                            } else {
                                // Create unavailable text if it doesn't exist
                                const newUnavailableText = document.createElement('span');
                                newUnavailableText.className = 'unavailable-text';
                                newUnavailableText.textContent = 'Unavailable';
                                scanTimeInputsDefault.appendChild(newUnavailableText);
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Generate time options with a specific value selected
     * @param {string} selectedValue - The value to select
     */
    generateTimeOptionsWithSelected(selectedValue) {
        // Use single global time configuration for all available options
        return this.generateTimeOptionsHTML(this.timeConfig.options, selectedValue);
    }

    /**
     * Validate business hours configuration
     * Ensures at least one day has hours set if the day is toggled on
     * @param {string} prefix - Either 'biz' or 'scan'
     * @returns {boolean} - True if valid, false otherwise
     */
    validateBusinessHours(prefix) {
        const days = this.DAYS_CONFIG.KEYS;

        // Check if at least one day has hours configured
        for (const day of days) {
            const toggle = document.getElementById(`${prefix}-${day}`);
            const startTime = document.getElementById(`${prefix}-${day}-start`);
            const endTime = document.getElementById(`${prefix}-${day}-end`);

            if (toggle && toggle.checked && startTime && endTime && startTime.value && endTime.value) {
                return true; // Found at least one configured day
            }
        }

        return false; // No days configured
    }

    /**
     * Save current data and dispatch change event
     * This method can be overridden or provided by the main form class
     */
    saveCurrentData() {
        console.log('BusinessHoursManager: saveCurrentData called - implement this in your main form class');
        this.dispatchChangeEvent();
    }

    /**
     * Dispatch custom change event with business hours data
     * Similar to custom-select.js event system
     */
    dispatchChangeEvent() {
        const businessHoursData = this.getHoursData('biz');
        const scannerHoursData = this.getHoursData('scan');

        const event = new CustomEvent('businessHoursChange', {
            detail: {
                businessHours: businessHoursData,
                scannerHours: scannerHoursData,
                allData: this.getAllHoursData(),
                timestamp: new Date().toISOString()
            },
            bubbles: true,
            cancelable: true
        });

        this.element.dispatchEvent(event);
    }

    /**
     * Dispatch specific event for business hours only
     */
    dispatchBusinessHoursChangeEvent() {
        const businessHoursData = this.getHoursData('biz');

        const event = new CustomEvent('businessHoursOnlyChange', {
            detail: {
                businessHours: businessHoursData,
                type: 'business',
                timestamp: new Date().toISOString()
            },
            bubbles: true,
            cancelable: true
        });

        this.element.dispatchEvent(event);
    }

    /**
     * Dispatch specific event for scanner hours only
     */
    dispatchScannerHoursChangeEvent() {
        const scannerHoursData = this.getHoursData('scan');

        const event = new CustomEvent('scannerHoursOnlyChange', {
            detail: {
                scannerHours: scannerHoursData,
                type: 'scanner',
                timestamp: new Date().toISOString()
            },
            bubbles: true,
            cancelable: true
        });

        this.element.dispatchEvent(event);
    }

    /**
     * Get all hours data in a comprehensive format
     * @returns {Object} - Complete hours data structure
     */
    getAllHoursData() {
        return {
            business: this.getHoursData('biz'),
            scanner: this.getHoursData('scan'),
            sameHours: this.isSameHoursEnabled(),
            validation: {
                businessValid: this.validateBusinessHours('biz'),
                scannerValid: this.validateBusinessHours('scan')
            }
        };
    }

    /**
     * Check if "same hours" option is enabled
     * @returns {boolean} - True if same hours is checked
     */
    isSameHoursEnabled() {
        const sameHoursCheck = document.getElementById('same-hours');
        return sameHoursCheck ? sameHoursCheck.checked : false;
    }

    /**
     * Public method to get current business hours data
     * @returns {Array} - Business hours data
     */
    getBusinessHours() {
        return this.getHoursData('biz');
    }

    /**
     * Public method to get current scanner hours data
     * @returns {Array} - Scanner hours data
     */
    getScannerHours() {
        return this.getHoursData('scan');
    }

    /**
     * Public method to get all data
     * @returns {Object} - All hours data
     */
    getData() {
        return this.getAllHoursData();
    }

    /**
     * Get business hours data in structured format
     * @param {string} prefix - Either 'biz' or 'scan'
     * @returns {Array} - Array of hours data for each day
     */
    getHoursData(prefix) {
        const days = this.DAYS_CONFIG.KEYS;
        const dayNames = this.DAYS_CONFIG.NAMES;
        const hoursData = [];

        days.forEach((day, index) => {
            const toggle = document.getElementById(`${prefix}-${day}`);

            // Determine if day is closed (unavailable)
            const isClosed = !toggle || !toggle.checked;

            const dayData = {
                day: dayNames[index],
                is_closed: isClosed,
                is_open_24: false, // This could be extended later if needed
                hours: []
            };

            // Only process hours if day is enabled (not closed)
            if (!isClosed) {
                const startTime = document.getElementById(`${prefix}-${day}-start`);
                const endTime = document.getElementById(`${prefix}-${day}-end`);

                if (startTime && endTime && startTime.value && endTime.value) {
                    dayData.hours.push({
                        open_time: startTime.value,
                        close_time: endTime.value
                    });
                }

                // Get additional time slots
                let slotIndex = 1;
                let startTimeSlot = document.getElementById(`${prefix}-${day}-start-${slotIndex}`);
                let endTimeSlot = document.getElementById(`${prefix}-${day}-end-${slotIndex}`);

                while (startTimeSlot && endTimeSlot && startTimeSlot.value && endTimeSlot.value) {
                    dayData.hours.push({
                        open_time: startTimeSlot.value,
                        close_time: endTimeSlot.value
                    });
                    slotIndex++;
                    startTimeSlot = document.getElementById(`${prefix}-${day}-start-${slotIndex}`);
                    endTimeSlot = document.getElementById(`${prefix}-${day}-end-${slotIndex}`);
                }
            }

            // Always add all days (both open and closed)
            hoursData.push(dayData);
        });

        return hoursData;
    }

    /**
     * Convert hours array back to flat format
     * @param {Array} hoursArray - Array of hours data
     * @param {string} prefix - Either 'biz' or 'scan'
     * @returns {Object} - Flat hours data object
     */
    convertHoursToFlat(hoursArray, prefix) {
        const flatHours = {};
        const dayMap = this.DAYS_CONFIG.DAY_MAP;

        hoursArray.forEach(dayData => {
            const dayKey = dayMap[dayData.day];
            if (dayKey) {
                // Set toggle based on is_closed status
                if (!dayData.is_closed && dayData.hours && dayData.hours.length > 0) {
                    flatHours[`${prefix}-${dayKey}`] = 'on';

                    dayData.hours.forEach((slot, index) => {
                        const suffix = index === 0 ? '' : `-${index}`;
                        flatHours[`${prefix}-${dayKey}-start${suffix}`] = slot.open_time;
                        flatHours[`${prefix}-${dayKey}-end${suffix}`] = slot.close_time;
                    });
                }
            }
        });

        return flatHours;
    }

    /**
     * Restore additional time slots that were added dynamically
     * @param {Object} data - Form data containing time slot information
     */
    restoreDynamicTimeSlots(data) {
        const prefixes = this.HOURS_CONFIG.PREFIXES;
        const days = this.DAYS_CONFIG.KEYS;

        prefixes.forEach(prefix => {
            days.forEach(day => {
                // Look for additional time slots (start-1, start-2, etc.)
                let slotIndex = 1;
                while (data[`${prefix}-${day}-start-${slotIndex}`] && data[`${prefix}-${day}-end-${slotIndex}`]) {
                    // Find the day row
                    const toggle = document.getElementById(`${prefix}-${day}`);
                    if (toggle) {
                        const row = toggle.closest('.bd-hours-row');
                        if (row) {
                            // Add the additional time slot
                            this.addTimeSlotForDay(row, prefix, day, slotIndex,
                                data[`${prefix}-${day}-start-${slotIndex}`],
                                data[`${prefix}-${day}-end-${slotIndex}`]);
                        }
                    }
                    slotIndex++;
                }
            });
        });
    }

    /**
     * Add a time slot for a specific day with given values
     * @param {HTMLElement} row - The day row element
     * @param {string} prefix - Either 'biz' or 'scan'
     * @param {string} day - Day key
     * @param {number} slotIndex - The slot index
     * @param {string} startTime - Start time value
     * @param {string} endTime - End time value
     */
    addTimeSlotForDay(row, prefix, day, slotIndex, startTime, endTime) {
        const timeInputsContainer = row.querySelector('.time-inputs-container');

        // Create a new time slot div
        const newTimeSlot = document.createElement('div');
        newTimeSlot.className = 'additional-time-slot';

        // Create time inputs with the saved values
        const timeInputs = document.createElement('div');
        timeInputs.className = 'time-inputs';

        // Generate options but select the saved values
        const startOptions = this.generateTimeOptionsWithSelected(startTime);
        const endOptions = this.generateTimeOptionsWithSelected(endTime);

        timeInputs.innerHTML = `
                <select id="${prefix}-${day}-start-${slotIndex}" name="${prefix}-${day}-start-${slotIndex}" class="time-select">
                    ${startOptions}
                </select>
                <span>-</span>
                <select id="${prefix}-${day}-end-${slotIndex}" name="${prefix}-${day}-end-${slotIndex}" class="time-select">
                    ${endOptions}
                </select>
            `;

        // Create remove button
        const removeBtn = document.createElement('button');
        removeBtn.className = 'add-hours-btn remove-hours-btn';
        removeBtn.type = 'button';
        removeBtn.style.backgroundColor = "#EBEBEB";
        removeBtn.setAttribute('aria-label', 'Remove time slot');

        removeBtn.addEventListener('click', () => {
            this.removeTimeSlot(newTimeSlot);
            // Auto-save data after removing time slot
            setTimeout(() => {
                this.saveCurrentData();
            }, 350); // Slight delay to ensure removal animation completes
        });

        // Add time inputs and remove button to the new time slot
        newTimeSlot.appendChild(timeInputs);
        newTimeSlot.appendChild(removeBtn);

        // Set up for future animations
        newTimeSlot.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

        // Add the additional time slot at the top (right after time-inputs-default)
        const timeInputsDefault = timeInputsContainer.querySelector('.time-inputs-default');
        if (timeInputsDefault && timeInputsDefault.nextSibling) {
            timeInputsContainer.insertBefore(newTimeSlot, timeInputsDefault.nextSibling);
        } else {
            timeInputsContainer.appendChild(newTimeSlot);
        }
    }

    /**
     * Update toggle states and corresponding UI
     * @param {Object} data - Form data containing toggle states
     */
    updateToggleStates(data) {
        const prefixes = this.HOURS_CONFIG.PREFIXES;
        const days = this.DAYS_CONFIG.KEYS;

        prefixes.forEach(prefix => {
            days.forEach(day => {
                const toggle = document.getElementById(`${prefix}-${day}`);
                if (toggle) {
                    const isChecked = data[`${prefix}-${day}`] === 'on';
                    toggle.checked = isChecked;

                    // Update the UI to match the toggle state
                    this.toggleDayAvailability(toggle);
                }
            });
        });
    }

    /**
     * Initialize the business hours manager
     * This should be called after the DOM is ready
     */
    init() {
        this.initializeFacilityInfoStep();
    }
}

// Static instance management
BusinessHoursManager.instance = null;

/**
 * Static method to get the current instance
 * @returns {BusinessHoursManager|null} - Current instance or null
 */
BusinessHoursManager.getInstance = function () {
    return BusinessHoursManager.instance;
};

/**
 * Static method to initialize or get existing instance
 * @param {Element} element - Element to dispatch events on (optional)
 * @returns {BusinessHoursManager} - The instance
 */
BusinessHoursManager.init = function (element = document) {
    if (!BusinessHoursManager.instance) {
        BusinessHoursManager.instance = new BusinessHoursManager(element);
        BusinessHoursManager.instance.init();
    }
    return BusinessHoursManager.instance;
};

/**
 * Static method to get business hours data
 * @returns {Array|null} - Business hours data or null if no instance
 */
BusinessHoursManager.getBusinessHours = function () {
    const instance = BusinessHoursManager.getInstance();
    return instance ? instance.getBusinessHours() : null;
};

/**
 * Static method to get scanner hours data
 * @returns {Array|null} - Scanner hours data or null if no instance
 */
BusinessHoursManager.getScannerHours = function () {
    const instance = BusinessHoursManager.getInstance();
    return instance ? instance.getScannerHours() : null;
};

/**
 * Static method to get all hours data
 * @returns {Object|null} - All hours data or null if no instance
 */
BusinessHoursManager.getAllData = function () {
    const instance = BusinessHoursManager.getInstance();
    return instance ? instance.getData() : null;
};

// Initialize the form when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    BusinessHoursManager.init();
});

// Make BusinessHoursManager globally accessible
if (typeof window !== 'undefined') {
    window.BusinessHoursManager = BusinessHoursManager;
}