class CustomDatePicker {
  constructor(inputElement, options = {}) {
    this.inputElement = inputElement;
    this.options = {
      format: 'MM/dd/yyyy',
      defaultDate: new Date(2000, 1, 23), // February 23, 2000
      mode: 'day', // 'day', 'week'
      minYear: 1900,
      maxYear: 2100,
      ...options
    };

    this.selectedDate = null;
    this.selectedWeek = null; // For week mode
    this.viewMonth = new Date().getMonth();
    this.viewYear = new Date().getFullYear();
    this.currentView = this.options.mode === 'week' ? 'week' : 'day'; // 'decade', 'year', 'month', 'day', 'week'
    this.viewDecade = Math.floor(this.viewYear / 10) * 10;
    this.calendar = null;

    // Ensure view year is within bounds
    this.viewYear = Math.max(this.options.minYear, Math.min(this.options.maxYear, this.viewYear));
    this.viewDecade = Math.floor(this.viewYear / 10) * 10;

    this.init();
  }

  init() {
    // Set initial date
    if (this.options.defaultDate) {
      this.setDate(this.options.defaultDate);
    }

    // Create calendar element
    this.createCalendar();

    // Attach event listeners
    this.attachEventListeners();
  }

  createCalendar() {
    // Create calendar container
    this.calendar = document.createElement('div');
    this.calendar.className = 'custom-datepicker-calendar hidden';

    // Add styles if not already added
    if (!document.getElementById('custom-datepicker-styles')) {
      this.addStyles();
    }

    // Position calendar relative to input
    this.inputElement.parentNode.style.position = 'relative';
    this.inputElement.parentNode.appendChild(this.calendar);
  }

  addStyles() {
    const style = document.createElement('style');
    style.id = 'custom-datepicker-styles';
    style.textContent = `
      .custom-datepicker-calendar {
        position: absolute;
        top: calc(100% + 8px);
        left: 0;
        background: #2C3E50;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        padding: 16px;
        z-index: 1000;
        width: 320px;
        color: #fff;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      .custom-datepicker-calendar.hidden {
        display: none;
      }
      .custom-datepicker-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding: 0 8px;
      }
      .custom-datepicker-nav-button {
        background: none;
        border: none;
        color: #fff;
        font-size: 18px;
        cursor: pointer;
        padding: 8px;
        border-radius: 6px;
        transition: background 0.2s;
      }
      .custom-datepicker-nav-button:hover {
        background: rgba(255,255,255,0.1);
      }
      .custom-datepicker-header-title {
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        padding: 8px 16px;
        border-radius: 6px;
        transition: background 0.2s;
      }
      .custom-datepicker-header-title:hover {
        background: rgba(255,255,255,0.1);
      }
      .custom-datepicker-view-grid {
        display: grid;
        gap: 8px;
      }
      .custom-datepicker-decade-grid {
        grid-template-columns: repeat(4, 1fr);
      }
      .custom-datepicker-year-grid {
        grid-template-columns: repeat(4, 1fr);
      }
      .custom-datepicker-month-grid {
        grid-template-columns: repeat(4, 1fr);
      }
      .custom-datepicker-day-grid {
        grid-template-columns: repeat(7, 1fr);
      }
      .custom-datepicker-grid-item {
        text-align: center;
        padding: 12px 8px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 14px;
        font-weight: 500;
      }
      .custom-datepicker-grid-item:hover {
        background: rgba(255,255,255,0.1);
      }
      .custom-datepicker-grid-item.selected {
        background: #4A90E2;
        color: #fff;
      }
      .custom-datepicker-grid-item.today {
        background: rgba(74, 144, 226, 0.3);
      }
      .custom-datepicker-day-name {
        font-weight: 600;
        cursor: default;
        color: #BDC3C7;
        font-size: 12px;
        padding: 8px;
      }
      .custom-datepicker-day-name:hover {
        background: none;
      }
      .custom-datepicker-other-month {
        color: #7F8C8D;
      }
      .custom-datepicker-week-grid {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
      .custom-datepicker-week-header {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(255,255,255,0.2);
        margin-bottom: 8px;
      }
      .custom-datepicker-week-number-header {
        width: 60px;
        text-align: center;
        font-weight: 600;
        color: #BDC3C7;
        font-size: 12px;
      }
      .custom-datepicker-week-days-header {
        flex: 1;
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 4px;
        text-align: center;
        font-weight: 600;
        color: #BDC3C7;
        font-size: 12px;
      }
      .custom-datepicker-week-row {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s;
      }
      .custom-datepicker-week-row:hover {
        background: rgba(255,255,255,0.1);
      }
      .custom-datepicker-week-row.selected {
        background: #4A90E2;
        color: #fff;
      }
      .custom-datepicker-week-number {
        width: 60px;
        text-align: center;
        font-weight: 600;
        font-size: 14px;
      }
      .custom-datepicker-week-days {
        flex: 1;
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 4px;
        text-align: center;
      }
      .custom-datepicker-week-day {
        padding: 4px;
        font-size: 12px;
        border-radius: 4px;
      }
      .custom-datepicker-week-day.today {
        background: rgba(74, 144, 226, 0.3);
      }
      .custom-datepicker-week-day.other-month {
        color: #7F8C8D;
      }
      .custom-datepicker-nav-button:disabled {
        opacity: 0.3;
        cursor: not-allowed;
        pointer-events: none;
      }
      .custom-datepicker-grid-item.custom-datepicker-disabled {
        color: #7F8C8D;
        cursor: not-allowed;
        opacity: 0.5;
        pointer-events: none;
      }
      .custom-datepicker-grid-item.custom-datepicker-disabled:hover {
        background: none;
      }
    `;
    document.head.appendChild(style);
  }

  attachEventListeners() {
    // Clicking the input toggles calendar
    this.inputElement.addEventListener("click", (e) => {
      e.stopPropagation();
      this.toggleCalendar();
    });

    // Smart: clicking inside calendar does NOT close it
    this.calendar.addEventListener("click", (e) => {
      e.stopPropagation();
    });

    // Clicking outside closes calendar
    document.addEventListener("click", () => {
      this.closeCalendar();
    });

    // Keyboard support
    this.inputElement.addEventListener("keydown", (e) => {
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        this.toggleCalendar();
      }
    });
  }

  toggleCalendar() {
    if (this.calendar.classList.contains('hidden')) {
      this.openCalendar();
    } else {
      this.closeCalendar();
    }
  }

  openCalendar() {
    this.calendar.classList.remove('hidden');
    this.currentView = this.options.mode === 'week' ? 'week' : 'day';
    this.renderCalendar();
  }

  closeCalendar() {
    this.calendar.classList.add('hidden');
  }

  setDate(date) {
    this.selectedDate = date;
    this.viewYear = date.getFullYear();
    this.viewMonth = date.getMonth();
    this.viewDecade = Math.floor(this.viewYear / 10) * 10;
    this.updateDateDisplay();
  }

  updateDateDisplay() {
    if (this.options.mode === 'week' && this.selectedWeek) {
      this.inputElement.value = `${this.selectedWeek.year}-W${String(this.selectedWeek.week).padStart(2, '0')}`;
    } else if (this.selectedDate) {
      const mm = String(this.selectedDate.getMonth() + 1).padStart(2, "0");
      const dd = String(this.selectedDate.getDate()).padStart(2, "0");
      const yyyy = this.selectedDate.getFullYear();
      this.inputElement.value = `${mm}/${dd}/${yyyy}`;
    }
  }

  renderCalendar() {
    switch (this.currentView) {
      case 'decade':
        this.renderDecadeView();
        break;
      case 'year':
        this.renderYearView();
        break;
      case 'month':
        this.renderMonthView();
        break;
      case 'week':
        this.renderWeekView();
        break;
      case 'day':
      default:
        this.renderDayView();
        break;
    }
  }

  renderDecadeView() {
    const startDecade = this.viewDecade;
    const endDecade = startDecade + 9;

    let html = `
      <div class="custom-datepicker-header">
        <button class="custom-datepicker-nav-button" data-action="prev-decade" ${this.canNavigateToPrevDecade() ? '' : 'disabled'}>‹</button>
        <div class="custom-datepicker-header-title">${startDecade}-${endDecade}</div>
        <button class="custom-datepicker-nav-button" data-action="next-decade" ${this.canNavigateToNextDecade() ? '' : 'disabled'}>›</button>
      </div>
      <div class="custom-datepicker-view-grid custom-datepicker-decade-grid">
    `;

    // Add previous decade year if within range
    const prevYear = startDecade - 1;
    if (prevYear >= this.options.minYear) {
      html += `<div class="custom-datepicker-grid-item custom-datepicker-other-month" data-year="${prevYear}">${prevYear}</div>`;
    } else {
      html += `<div class="custom-datepicker-grid-item custom-datepicker-disabled"></div>`;
    }

    // Add current decade years
    for (let year = startDecade; year <= endDecade; year++) {
      const isSelected = this.selectedDate && this.selectedDate.getFullYear() === year;
      const isToday = new Date().getFullYear() === year;
      const isDisabled = year < this.options.minYear || year > this.options.maxYear;

      if (isDisabled) {
        html += `<div class="custom-datepicker-grid-item custom-datepicker-disabled">${year}</div>`;
      } else {
        html += `<div class="custom-datepicker-grid-item ${isSelected ? 'selected' : ''} ${isToday ? 'today' : ''}" data-year="${year}">${year}</div>`;
      }
    }

    // Add next decade years if within range
    for (let year = endDecade + 1; year <= endDecade + 2; year++) {
      if (year <= this.options.maxYear) {
        html += `<div class="custom-datepicker-grid-item custom-datepicker-other-month" data-year="${year}">${year}</div>`;
      } else {
        html += `<div class="custom-datepicker-grid-item custom-datepicker-disabled"></div>`;
      }
    }

    html += `</div>`;
    this.calendar.innerHTML = html;
    this.attachDecadeListeners();
  }

  renderYearView() {
    const startYear = this.viewDecade;
    const endYear = startYear + 9;

    let html = `
      <div class="custom-datepicker-header">
        <button class="custom-datepicker-nav-button" data-action="prev-decade" ${this.canNavigateToPrevDecade() ? '' : 'disabled'}>‹</button>
        <div class="custom-datepicker-header-title" data-action="show-decade">${startYear}-${endYear}</div>
        <button class="custom-datepicker-nav-button" data-action="next-decade" ${this.canNavigateToNextDecade() ? '' : 'disabled'}>›</button>
      </div>
      <div class="custom-datepicker-view-grid custom-datepicker-year-grid">
    `;

    for (let year = startYear; year <= endYear; year++) {
      const isSelected = this.viewYear === year;
      const isToday = new Date().getFullYear() === year;
      const isDisabled = year < this.options.minYear || year > this.options.maxYear;

      if (isDisabled) {
        html += `<div class="custom-datepicker-grid-item custom-datepicker-disabled">${year}</div>`;
      } else {
        html += `<div class="custom-datepicker-grid-item ${isSelected ? 'selected' : ''} ${isToday ? 'today' : ''}" data-year="${year}">${year}</div>`;
      }
    }

    html += `</div>`;
    this.calendar.innerHTML = html;
    this.attachYearListeners();
  }

  renderMonthView() {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    let html = `
      <div class="custom-datepicker-header">
        <button class="custom-datepicker-nav-button" data-action="prev-year" ${this.canNavigateToPrevYear() ? '' : 'disabled'}>‹</button>
        <div class="custom-datepicker-header-title" data-action="show-year">${this.viewYear}</div>
        <button class="custom-datepicker-nav-button" data-action="next-year" ${this.canNavigateToNextYear() ? '' : 'disabled'}>›</button>
      </div>
      <div class="custom-datepicker-view-grid custom-datepicker-month-grid">
    `;

    months.forEach((month, index) => {
      const isSelected = this.viewMonth === index;
      const isToday = new Date().getFullYear() === this.viewYear && new Date().getMonth() === index;
      html += `<div class="custom-datepicker-grid-item ${isSelected ? 'selected' : ''} ${isToday ? 'today' : ''}" data-month="${index}">${month}</div>`;
    });

    html += `</div>`;
    this.calendar.innerHTML = html;
    this.attachMonthListeners();
  }

  renderDayView() {
    const firstDay = new Date(this.viewYear, this.viewMonth, 1).getDay();
    const daysInMonth = new Date(this.viewYear, this.viewMonth + 1, 0).getDate();
    const daysInPrevMonth = new Date(this.viewYear, this.viewMonth, 0).getDate();
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'];

    let html = `
      <div class="custom-datepicker-header">
        <button class="custom-datepicker-nav-button" data-action="prev-month" ${this.canNavigateToPrevMonth() ? '' : 'disabled'}>‹</button>
        <div class="custom-datepicker-header-title" data-action="show-month">${monthNames[this.viewMonth]} ${this.viewYear}</div>
        <button class="custom-datepicker-nav-button" data-action="next-month" ${this.canNavigateToNextMonth() ? '' : 'disabled'}>›</button>
      </div>
      <div class="custom-datepicker-view-grid custom-datepicker-day-grid">
    `;

    // Day names
    ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].forEach(day => {
      html += `<div class="custom-datepicker-grid-item custom-datepicker-day-name">${day}</div>`;
    });

    // Previous month days
    for (let i = firstDay - 1; i >= 0; i--) {
      const day = daysInPrevMonth - i;
      html += `<div class="custom-datepicker-grid-item custom-datepicker-other-month" data-day="${day}" data-month="${this.viewMonth - 1}">${day}</div>`;
    }

    // Current month days
    for (let day = 1; day <= daysInMonth; day++) {
      const isSelected = this.selectedDate &&
        this.selectedDate.getDate() === day &&
        this.selectedDate.getMonth() === this.viewMonth &&
        this.selectedDate.getFullYear() === this.viewYear;
      const today = new Date();
      const isToday = today.getDate() === day &&
        today.getMonth() === this.viewMonth &&
        today.getFullYear() === this.viewYear;
      html += `<div class="custom-datepicker-grid-item ${isSelected ? 'selected' : ''} ${isToday ? 'today' : ''}" data-day="${day}">${day}</div>`;
    }

    // Next month days
    const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;
    const remainingCells = totalCells - (firstDay + daysInMonth);
    for (let day = 1; day <= remainingCells; day++) {
      html += `<div class="custom-datepicker-grid-item custom-datepicker-other-month" data-day="${day}" data-month="${this.viewMonth + 1}">${day}</div>`;
    }

    html += `</div>`;
    this.calendar.innerHTML = html;
    this.attachDayListeners();
  }

  renderWeekView() {
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'];

    let html = `
      <div class="custom-datepicker-header">
        <button class="custom-datepicker-nav-button" data-action="prev-month" ${this.canNavigateToPrevMonth() ? '' : 'disabled'}>‹</button>
        <div class="custom-datepicker-header-title" data-action="show-month">${monthNames[this.viewMonth]} ${this.viewYear}</div>
        <button class="custom-datepicker-nav-button" data-action="next-month" ${this.canNavigateToNextMonth() ? '' : 'disabled'}>›</button>
      </div>
      <div class="custom-datepicker-view-grid custom-datepicker-week-grid">
    `;

    // Week header
    html += `
      <div class="custom-datepicker-week-header">
        <div class="custom-datepicker-week-number-header">Week</div>
        <div class="custom-datepicker-week-days-header">
          <span>Su</span><span>Mo</span><span>Tu</span><span>We</span><span>Th</span><span>Fr</span><span>Sa</span>
        </div>
      </div>
    `;

    // Get weeks for the current month
    const weeks = this.getWeeksInMonth(this.viewYear, this.viewMonth);

    weeks.forEach(week => {
      const isSelected = this.selectedWeek &&
        this.selectedWeek.year === week.year &&
        this.selectedWeek.week === week.week;

      html += `
        <div class="custom-datepicker-week-row ${isSelected ? 'selected' : ''}"
             data-year="${week.year}"
             data-week="${week.week}">
          <div class="custom-datepicker-week-number">${week.week}</div>
          <div class="custom-datepicker-week-days">
      `;

      week.days.forEach(day => {
        const isCurrentMonth = day.month === this.viewMonth;
        const isToday = this.isToday(day.date);
        html += `
          <span class="custom-datepicker-week-day ${!isCurrentMonth ? 'other-month' : ''} ${isToday ? 'today' : ''}">
            ${day.date.getDate()}
          </span>
        `;
      });

      html += `
          </div>
        </div>
      `;
    });

    html += `</div>`;
    this.calendar.innerHTML = html;
    this.attachWeekListeners();
  }

  getWeeksInMonth(year, month) {
    const weeks = [];
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    // Start from the beginning of the week containing the first day
    let currentDate = new Date(firstDay);
    currentDate.setDate(currentDate.getDate() - currentDate.getDay());

    while (currentDate <= lastDay || currentDate.getMonth() === month) {
      const weekStart = new Date(currentDate);
      const weekNumber = this.getWeekNumber(weekStart);
      const weekYear = this.getWeekYear(weekStart);

      const week = {
        year: weekYear,
        week: weekNumber,
        days: []
      };

      // Add 7 days to the week
      for (let i = 0; i < 7; i++) {
        const dayDate = new Date(currentDate);
        week.days.push({
          date: dayDate,
          month: dayDate.getMonth()
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }

      weeks.push(week);

      // Break if we've gone past the month and have at least one week
      if (currentDate.getMonth() !== month && weeks.length > 0) {
        break;
      }
    }

    return weeks;
  }

  getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  }

  getWeekYear(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    return d.getUTCFullYear();
  }

  isToday(date) {
    const today = new Date();
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  }

  // Navigation helper methods
  canNavigateToPrevDecade() {
    return this.viewDecade - 10 >= Math.floor(this.options.minYear / 10) * 10;
  }

  canNavigateToNextDecade() {
    return this.viewDecade + 10 <= Math.floor(this.options.maxYear / 10) * 10;
  }

  canNavigateToPrevYear() {
    return this.viewYear - 1 >= this.options.minYear;
  }

  canNavigateToNextYear() {
    return this.viewYear + 1 <= this.options.maxYear;
  }

  canNavigateToPrevMonth() {
    if (this.viewYear > this.options.minYear) return true;
    if (this.viewYear === this.options.minYear) return this.viewMonth > 0;
    return false;
  }

  canNavigateToNextMonth() {
    if (this.viewYear < this.options.maxYear) return true;
    if (this.viewYear === this.options.maxYear) return this.viewMonth < 11;
    return false;
  }

  attachDecadeListeners() {
    // Navigation buttons
    this.calendar.querySelector('[data-action="prev-decade"]')?.addEventListener('click', () => {
      if (this.canNavigateToPrevDecade()) {
        this.viewDecade -= 10;
        this.renderDecadeView();
      }
    });

    this.calendar.querySelector('[data-action="next-decade"]')?.addEventListener('click', () => {
      if (this.canNavigateToNextDecade()) {
        this.viewDecade += 10;
        this.renderDecadeView();
      }
    });

    // Year selection
    this.calendar.querySelectorAll('[data-year]').forEach(yearEl => {
      yearEl.addEventListener('click', () => {
        const year = parseInt(yearEl.dataset.year);
        if (year >= this.options.minYear && year <= this.options.maxYear) {
          this.viewYear = year;
          this.viewDecade = Math.floor(this.viewYear / 10) * 10;
          this.currentView = 'year';
          this.renderYearView();
        }
      });
    });
  }

  attachYearListeners() {
    // Navigation buttons
    this.calendar.querySelector('[data-action="prev-decade"]')?.addEventListener('click', () => {
      if (this.canNavigateToPrevDecade()) {
        this.viewDecade -= 10;
        this.renderYearView();
      }
    });

    this.calendar.querySelector('[data-action="next-decade"]')?.addEventListener('click', () => {
      if (this.canNavigateToNextDecade()) {
        this.viewDecade += 10;
        this.renderYearView();
      }
    });

    // Header title click to go back to decade view
    this.calendar.querySelector('[data-action="show-decade"]')?.addEventListener('click', () => {
      this.currentView = 'decade';
      this.renderDecadeView();
    });

    // Year selection
    this.calendar.querySelectorAll('[data-year]').forEach(yearEl => {
      yearEl.addEventListener('click', () => {
        const year = parseInt(yearEl.dataset.year);
        if (year >= this.options.minYear && year <= this.options.maxYear) {
          this.viewYear = year;
          this.currentView = 'month';
          this.renderMonthView();
        }
      });
    });
  }

  attachMonthListeners() {
    // Navigation buttons
    this.calendar.querySelector('[data-action="prev-year"]')?.addEventListener('click', () => {
      if (this.canNavigateToPrevYear()) {
        this.viewYear--;
        this.viewDecade = Math.floor(this.viewYear / 10) * 10;
        this.renderMonthView();
      }
    });

    this.calendar.querySelector('[data-action="next-year"]')?.addEventListener('click', () => {
      if (this.canNavigateToNextYear()) {
        this.viewYear++;
        this.viewDecade = Math.floor(this.viewYear / 10) * 10;
        this.renderMonthView();
      }
    });

    // Header title click to go back to year view
    this.calendar.querySelector('[data-action="show-year"]')?.addEventListener('click', () => {
      this.currentView = 'year';
      this.renderYearView();
    });

    // Month selection
    this.calendar.querySelectorAll('[data-month]').forEach(monthEl => {
      monthEl.addEventListener('click', () => {
        this.viewMonth = parseInt(monthEl.dataset.month);
        this.currentView = this.options.mode === 'week' ? 'week' : 'day';
        if (this.options.mode === 'week') {
          this.renderWeekView();
        } else {
          this.renderDayView();
        }
      });
    });
  }

  attachDayListeners() {
    // Navigation buttons
    this.calendar.querySelector('[data-action="prev-month"]')?.addEventListener('click', () => {
      if (this.canNavigateToPrevMonth()) {
        this.viewMonth--;
        if (this.viewMonth < 0) {
          this.viewMonth = 11;
          this.viewYear--;
          this.viewDecade = Math.floor(this.viewYear / 10) * 10;
        }
        this.renderDayView();
      }
    });

    this.calendar.querySelector('[data-action="next-month"]')?.addEventListener('click', () => {
      if (this.canNavigateToNextMonth()) {
        this.viewMonth++;
        if (this.viewMonth > 11) {
          this.viewMonth = 0;
          this.viewYear++;
          this.viewDecade = Math.floor(this.viewYear / 10) * 10;
        }
        this.renderDayView();
      }
    });

    // Header title click to go back to month view
    this.calendar.querySelector('[data-action="show-month"]')?.addEventListener('click', () => {
      this.currentView = 'month';
      this.renderMonthView();
    });

    // Day selection
    this.calendar.querySelectorAll('[data-day]:not(.custom-datepicker-day-name)').forEach(dayEl => {
      dayEl.addEventListener('click', () => {
        const day = parseInt(dayEl.dataset.day);
        let month = this.viewMonth;
        let year = this.viewYear;

        // Handle previous/next month days
        if (dayEl.classList.contains('custom-datepicker-other-month')) {
          if (dayEl.dataset.month !== undefined) {
            month = parseInt(dayEl.dataset.month);
            if (month < 0) {
              month = 11;
              year--;
            } else if (month > 11) {
              month = 0;
              year++;
            }
          }
        }

        this.selectedDate = new Date(year, month, day);
        this.viewYear = year;
        this.viewMonth = month;
        this.viewDecade = Math.floor(year / 10) * 10;
        this.updateDateDisplay();
        this.closeCalendar();

        // Dispatch custom event
        this.inputElement.dispatchEvent(new CustomEvent('datechange', {
          detail: { date: this.selectedDate }
        }));
      });
    });
  }

  attachWeekListeners() {
    // Navigation buttons
    this.calendar.querySelector('[data-action="prev-month"]')?.addEventListener('click', () => {
      if (this.canNavigateToPrevMonth()) {
        this.viewMonth--;
        if (this.viewMonth < 0) {
          this.viewMonth = 11;
          this.viewYear--;
          this.viewDecade = Math.floor(this.viewYear / 10) * 10;
        }
        this.renderWeekView();
      }
    });

    this.calendar.querySelector('[data-action="next-month"]')?.addEventListener('click', () => {
      if (this.canNavigateToNextMonth()) {
        this.viewMonth++;
        if (this.viewMonth > 11) {
          this.viewMonth = 0;
          this.viewYear++;
          this.viewDecade = Math.floor(this.viewYear / 10) * 10;
        }
        this.renderWeekView();
      }
    });

    // Header title click to go back to month view
    this.calendar.querySelector('[data-action="show-month"]')?.addEventListener('click', () => {
      this.currentView = 'month';
      this.renderMonthView();
    });

    // Week selection
    this.calendar.querySelectorAll('.custom-datepicker-week-row').forEach(weekEl => {
      weekEl.addEventListener('click', () => {
        const year = parseInt(weekEl.dataset.year);
        const week = parseInt(weekEl.dataset.week);

        this.selectedWeek = { year, week };
        this.updateDateDisplay();
        this.closeCalendar();

        // Dispatch custom event
        this.inputElement.dispatchEvent(new CustomEvent('weekchange', {
          detail: {
            week: this.selectedWeek,
            weekStart: this.getWeekStartDate(year, week),
            weekEnd: this.getWeekEndDate(year, week)
          }
        }));
      });
    });
  }

  getWeekStartDate(year, week) {
    const jan1 = new Date(year, 0, 1);
    const daysToAdd = (week - 1) * 7 - jan1.getDay();
    const weekStart = new Date(year, 0, 1 + daysToAdd);
    return weekStart;
  }

  getWeekEndDate(year, week) {
    const weekStart = this.getWeekStartDate(year, week);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 6);
    return weekEnd;
  }

  // Static method to initialize datepicker on elements
  static init(selector, options = {}) {
    const elements = document.querySelectorAll(selector);
    const instances = [];

    elements.forEach(element => {
      instances.push(new CustomDatePicker(element, options));
    });

    return instances.length === 1 ? instances[0] : instances;
  }

  // Method to destroy the datepicker
  destroy() {
    if (this.calendar && this.calendar.parentNode) {
      this.calendar.parentNode.removeChild(this.calendar);
    }
  }
}