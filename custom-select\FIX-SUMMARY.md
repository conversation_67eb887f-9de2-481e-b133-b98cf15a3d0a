# Custom Select - Auto-Focus Fix Summary

## Issue Description

The custom-select component was automatically focusing on the "Select All" option when opening multi-select dropdowns, instead of focusing on the first regular option or the first selected option.

## Root Cause

The issue was in the `open()` method's focus logic. Here's what was happening:

1. **DOM Structure**: When rendering multi-select dropdowns with `selectAll: true`, the "Select All" option is rendered first (at DOM index 0), followed by regular options (starting at DOM index 1).

2. **Focus Logic Problem**: The original focus logic used `this.allOptions.findIndex()` which returns indices based on the `this.allOptions` array (0, 1, 2, etc.), but these indices didn't account for the "Select All" option taking up DOM index 0.

3. **Index Mismatch**: When `focusedIndex = 0` was set, it focused on DOM index 0 (the "Select All" option) instead of the first regular option.

## Solution

Updated the `open()` method to calculate the correct DOM index by adding an offset for the "Select All" option:

```javascript
// Calculate the DOM index offset for the "Select All" option
const hasSelectAll = this.options.selectAll && this.allOptions.length > 1;
const selectAllOffset = hasSelectAll ? 1 : 0;

// Set focus index: if we have a selected item, focus on it (plus offset for Select All)
// Otherwise, focus on the first regular option (index 0 + offset)
this.focusedIndex = firstSelectedIndex >= 0 ? firstSelectedIndex + selectAllOffset : selectAllOffset;
```

## Changes Made

### File: `custom-select/js/custom-select.js`

**Lines 490-518**: Updated the `open()` method's focus logic for multi-select dropdowns.

**Before:**
```javascript
if (this.isMultiple) {
  const firstSelectedIndex = this.allOptions.findIndex(opt =>
    this.selectedValues.includes(opt.value)
  );
  this.focusedIndex = firstSelectedIndex >= 0 ? firstSelectedIndex : 0;
}
```

**After:**
```javascript
if (this.isMultiple) {
  const firstSelectedIndex = this.allOptions.findIndex(opt =>
    this.selectedValues.includes(opt.value)
  );
  
  // Calculate the DOM index offset for the "Select All" option
  const hasSelectAll = this.options.selectAll && this.allOptions.length > 1;
  const selectAllOffset = hasSelectAll ? 1 : 0;
  
  // Set focus index: if we have a selected item, focus on it (plus offset for Select All)
  // Otherwise, focus on the first regular option (index 0 + offset)
  this.focusedIndex = firstSelectedIndex >= 0 ? firstSelectedIndex + selectAllOffset : selectAllOffset;
}
```

### File: `custom-select/js/custom-select.min.js`

Updated the minified version to include the same fix.

## Expected Behavior After Fix

1. **No Pre-selected Options**: When opening a multi-select dropdown with no pre-selected options, the first regular option (not "Select All") should be focused.

2. **With Pre-selected Options**: When opening a multi-select dropdown with pre-selected options, the first selected option should be focused.

3. **Select All Disabled**: When `selectAll: false`, the behavior should remain unchanged (focus on first selected or first regular option).

4. **Single-select**: Single-select dropdowns are unaffected by this change.

## Testing

A test file `test-fix.html` has been created to verify the fix works correctly across different scenarios:

- Test Case 1: Multi-select with no pre-selected options
- Test Case 2: Multi-select with pre-selected options  
- Test Case 3: Multi-select with Select All disabled

## Backward Compatibility

This fix maintains full backward compatibility:
- All existing functionality remains unchanged
- No breaking changes to the API
- Only the initial focus behavior is corrected
- Keyboard navigation continues to work as expected

## Files Modified

1. `custom-select/js/custom-select.js` - Main source file
2. `custom-select/js/custom-select.min.js` - Minified version
3. `custom-select/test-fix.html` - Test file (new)
4. `custom-select/FIX-SUMMARY.md` - This documentation (new)
