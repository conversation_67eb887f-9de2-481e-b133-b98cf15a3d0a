/* CSS Variables for Colors */
:root {
  --custom-select-primary: #c8277d;
  --custom-select-primary-light: rgba(236, 1, 2, 0.2);
  --custom-select-primary-hover: #f0f8ff;
  --custom-select-primary-selected: linear-gradient(
    90deg,
    rgba(240, 248, 255, 0.8),
    transparent
  );
  --custom-select-border: #d0d7de;
  --custom-select-border-hover: rgba(236, 1, 2, 0.2);
  --custom-select-border-dropdown: #e6eef7;
  --custom-select-bg: #fff;
  --custom-select-bg-hover: #f8f9fa;
  --custom-select-text: #333;
  --custom-select-text-secondary: #222;
  --custom-select-placeholder: #999;
  --custom-select-arrow: #7e7e7e;
  --custom-select-shadow: rgba(247, 6, 6, 0.18);
  --custom-select-divider: #eee;
  --custom-select-chip-bg: #e0e0e0;
  --custom-select-chip-text: #333;
  --custom-select-chip-remove: #666;
  --custom-select-chip-remove-hover: #333;
  --custom-select-checkbox-border: #c8277d;
  --custom-select-checkbox-check: #d22725;
  --custom-select-focus-ring: rgba(236, 1, 2, 0.2);
  --custom-select-focus-outline: #c8277d;
  --custom-select-focus-bg: rgba(200, 39, 125, 0.1);
}

.custom-select-container {
  position: relative;
  width: 100%;
  font-family: Inter, system-ui, -apple-system, "Segoe UI", Roboto,
    "Helvetica Neue", Arial;
}
.custom-select-trigger {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  user-select: none;
  border: 1.5px solid var(--custom-select-border);
  background: var(--custom-select-bg);
  transition: all 0.2s ease;
}
.custom-select-trigger:hover {
  background: var(--custom-select-bg-hover);
  border-color: var(--custom-select-border-hover);
}
.custom-select-trigger:focus {
  outline: 3px solid var(--custom-select-primary-light);
  border-color: var(--custom-select-primary);
}
.custom-select-trigger.open .custom-select-arrow {
  transform: rotate(180deg);
}
.custom-select-text {
  color: var(--custom-select-text);
  font-size: 14px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.custom-select-text.placeholder {
  color: var(--custom-select-placeholder);
}
.custom-select-arrow {
  font-size: 12px;
  margin-left: 8px;
  transition: transform 0.18s ease;
  color: var(--custom-select-arrow);
  display: flex;
  align-items: center;
}
.custom-select-dropdown {
  position: absolute;
  left: 0;
  right: 0;
  /* top: calc(100% + 4px); */
  top: 48px;
  z-index: 50;
  padding: 16px;
  background: var(--custom-select-bg);
  border-radius: 8px;
  border: 1px solid var(--custom-select-border-dropdown);
  max-height: 286px;
  overflow: hidden;
  display: none;
}

.custom-select-dropdown.open {
  display: block;
}

/* Items Wrapper - New container for all items */
.custom-select-items-wrapper {
  max-height: 260px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 8px 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.custom-select-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease;
  position: relative;
}
.custom-select-item:hover {
  background: var(--custom-select-primary-hover);
}

.custom-select-item.selected {
  background: var(--custom-select-primary-selected);
}

.custom-select-item.focused {
  background: var(--custom-select-focus-bg);
  outline: 2px solid var(--custom-select-focus-outline);
  outline-offset: -2px;
}

/* Focused state takes precedence over selected state for better visibility */
.custom-select-item.focused.selected {
  background: var(--custom-select-focus-bg);
  outline: 2px solid var(--custom-select-focus-outline);
  outline-offset: -2px;
}

.custom-select-select-all {
  border-bottom: 1px solid var(--custom-select-divider);
  margin-bottom: 8px;
  border-radius: 0;
  padding: 8px 0;
}

/* Custom Checkbox Styles */
.custom-select-checkbox {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.custom-select-item label {
  position: relative;
  padding-left: 28px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  color: var(--custom-select-text-secondary);
  margin-bottom: 0;
  width: 100%;
  user-select: none;
}

.custom-select-item label::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  border: 1px solid var(--custom-select-checkbox-border);
  border-radius: 4px;
  background: var(--custom-select-bg);
  transition: background 0.3s, box-shadow 0.3s;
  flex-shrink: 0;
}

.custom-select-item label::after {
  content: "";
  position: absolute;
  left: 7px;
  top: 50%;
  width: 5px;
  height: 10px;
  border-right: 1px solid var(--custom-select-checkbox-check);
  border-bottom: 1px solid var(--custom-select-checkbox-check);
  transform: translateY(-50%) rotate(45deg);
  opacity: 0;
  transition: opacity 0.3s;
}

.custom-select-checkbox:checked + label::after {
  opacity: 1;
}

.custom-select-checkbox:focus + label::before {
  box-shadow: 0 0 0 3px var(--custom-select-focus-ring);
}
.custom-select-item-text {
  font-size: 14px;
  color: var(--custom-select-text-secondary);
  flex: 1;
}
.custom-select-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}
.custom-select-chip {
  display: flex;
  align-items: center;
  background-color: var(--custom-select-chip-bg);
  border-radius: 16px;
  padding: 4px 8px;
  font-size: 12px;
  color: var(--custom-select-chip-text);
}
.custom-select-chip-remove {
  margin-left: 6px;
  cursor: pointer;
  font-weight: bold;
  color: var(--custom-select-chip-remove);
  font-size: 14px;
  line-height: 1;
}
.custom-select-chip-remove:hover {
  color: var(--custom-select-chip-remove-hover);
}
