<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Custom Date Picker</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto space-y-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-8">
        Custom Date Picker Examples
      </h1>

      <!-- Example 1: Basic Usage -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Basic Usage</h2>
        <div class="relative max-w-sm">
          <input
            id="basic-datepicker"
            type="text"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
            placeholder="Select date"
            readonly
          />
        </div>
      </div>

      <!-- Example 2: With Icon (Tailwind Style) -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">
          With Calendar Icon (Tailwind Style)
        </h2>
        <div class="relative max-w-sm">
          <div
            class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none"
          >
            <svg
              class="w-4 h-4 text-gray-500 dark:text-gray-400"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"
              />
            </svg>
          </div>
          <input
            id="tailwind-datepicker"
            type="text"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
            placeholder="Select date"
            readonly
          />
        </div>
      </div>

      <!-- Example 3: Multiple Date Pickers -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Multiple Date Pickers</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="relative">
            <label class="block text-sm font-medium text-gray-700 mb-2"
              >Start Date</label
            >
            <input
              id="start-date"
              type="text"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              placeholder="Select start date"
              readonly
            />
          </div>
          <div class="relative">
            <label class="block text-sm font-medium text-gray-700 mb-2"
              >End Date</label
            >
            <input
              id="end-date"
              type="text"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              placeholder="Select end date"
              readonly
            />
          </div>
        </div>
      </div>

      <!-- Example 4: Week Picker -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Week Picker</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="relative">
            <label class="block text-sm font-medium text-gray-700 mb-2"
              >Select Week</label
            >
            <input
              id="week-picker"
              type="text"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              placeholder="Select week"
              readonly
            />
          </div>
          <div class="relative">
            <label class="block text-sm font-medium text-gray-700 mb-2"
              >Week Range</label
            >
            <input
              id="week-range-display"
              type="text"
              class="bg-gray-100 border border-gray-300 text-gray-700 text-sm rounded-lg block w-full p-2.5"
              placeholder="Week range will appear here"
              readonly
            />
          </div>
        </div>
      </div>

      <!-- Example 5: Year Range Restrictions -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Year Range Restrictions</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="relative">
            <label class="block text-sm font-medium text-gray-700 mb-2"
              >Birth Date (1950-2010)</label
            >
            <input
              id="birth-date-picker"
              type="text"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              placeholder="Select birth date"
              readonly
            />
          </div>
          <div class="relative">
            <label class="block text-sm font-medium text-gray-700 mb-2"
              >Future Event (2024-2030)</label
            >
            <input
              id="future-event-picker"
              type="text"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
              placeholder="Select future date"
              readonly
            />
          </div>
        </div>
        <div class="mt-4 p-3 bg-blue-50 rounded-lg">
          <p class="text-sm text-blue-800">
            <strong>Note:</strong> These date pickers have restricted year
            ranges. Navigation arrows will be disabled when you reach the
            limits.
          </p>
        </div>
      </div>

      <!-- Example 6: Custom Styling -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">Custom Styling</h2>
        <div class="relative max-w-sm">
          <input
            id="custom-styled"
            type="text"
            class="bg-blue-50 border-2 border-blue-300 text-blue-900 text-lg rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-4 font-semibold"
            placeholder="Pick a date"
            readonly
          />
        </div>
      </div>
    </div>

    <script src="./js/date-picker.js"></script>
    <script>
      // Initialize date pickers
      document.addEventListener("DOMContentLoaded", function () {
        // Basic usage
        const basicPicker = new CustomDatePicker(
          document.getElementById("basic-datepicker")
        );

        // Tailwind style with icon
        const tailwindPicker = new CustomDatePicker(
          document.getElementById("tailwind-datepicker")
        );

        // Multiple date pickers
        const startDatePicker = new CustomDatePicker(
          document.getElementById("start-date")
        );
        const endDatePicker = new CustomDatePicker(
          document.getElementById("end-date")
        );

        // Week picker
        const weekPicker = new CustomDatePicker(
          document.getElementById("week-picker"),
          {
            mode: "week",
            defaultDate: new Date(2025, 7, 9), // August 9, 2025
          }
        );

        // Birth date picker with year range restriction
        const birthDatePicker = new CustomDatePicker(
          document.getElementById("birth-date-picker"),
          {
            defaultDate: new Date(1980, 0, 1), // January 1, 1980
            minYear: 1950,
            maxYear: 2010,
          }
        );

        // Future event picker with year range restriction
        const futureEventPicker = new CustomDatePicker(
          document.getElementById("future-event-picker"),
          {
            defaultDate: new Date(2025, 0, 1), // January 1, 2025
            minYear: 2024,
            maxYear: 2030,
          }
        );

        // Custom styled
        const customPicker = new CustomDatePicker(
          document.getElementById("custom-styled"),
          {
            defaultDate: new Date(2024, 0, 1), // January 1, 2024
          }
        );

        // Event listeners for demonstration
        document
          .getElementById("basic-datepicker")
          .addEventListener("datechange", function (e) {
            console.log("Basic picker date changed:", e.detail.date);
          });

        document
          .getElementById("tailwind-datepicker")
          .addEventListener("datechange", function (e) {
            console.log("Tailwind picker date changed:", e.detail.date);
          });

        // Week picker event listener
        document
          .getElementById("week-picker")
          .addEventListener("weekchange", function (e) {
            console.log("Week picker changed:", e.detail);

            // Update the week range display
            const weekStart = e.detail.weekStart;
            const weekEnd = e.detail.weekEnd;
            const formatDate = (date) => {
              return date.toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
                year: "numeric",
              });
            };

            document.getElementById("week-range-display").value = `${formatDate(
              weekStart
            )} - ${formatDate(weekEnd)}`;
          });

        // You can also use the static init method for multiple elements
        // CustomDatePicker.init('.datepicker-class');
      });
    </script>
  </body>
</html>
