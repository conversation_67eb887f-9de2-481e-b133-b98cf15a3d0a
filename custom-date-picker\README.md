# Custom Date Picker

A modern, accessible, and highly customizable date picker component with multi-level navigation (decade → year → month → day). Built with vanilla JavaScript and designed to work with any CSS framework.

## Features

- 🗓️ **Multi-level Navigation**: Navigate through decades, years, months, and days
- 📅 **Week Picker Mode**: Select entire weeks with week numbers
- 🎨 **Framework Agnostic**: Works with any CSS framework (Tailwind, Bootstrap, etc.)
- ♿ **Accessible**: Keyboard navigation and screen reader support
- 📱 **Responsive**: Works on all device sizes
- 🎯 **Easy Integration**: Simple JavaScript class that works with any input element
- 🔧 **Customizable**: Extensive styling and configuration options
- 🚀 **Lightweight**: No dependencies, pure vanilla JavaScript

## Demo

![Date Picker Demo](demo.gif)

## Quick Start

### 1. Include the JavaScript file

```html
<script src="path/to/date-picker.js"></script>
```

### 2. Create an input element

```html
<input id="my-datepicker" type="text" placeholder="Select date" readonly>
```

### 3. Initialize the date picker

```javascript
const datePicker = new CustomDatePicker(document.getElementById('my-datepicker'));
```

## Installation

### Option 1: Direct Download
Download the `date-picker.js` file and include it in your project.

### Option 2: Copy and Paste
Copy the JavaScript code from `js/date-picker.js` into your project.

## Usage Examples

### Basic Usage

```html
<div class="relative">
  <input 
    id="basic-datepicker" 
    type="text" 
    placeholder="Select date"
    readonly
  >
</div>

<script>
const picker = new CustomDatePicker(document.getElementById('basic-datepicker'));
</script>
```

### With Tailwind CSS (Your Example)

```html
<div class="relative max-w-sm">
  <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
    <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
      <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
    </svg>
  </div>
  <input 
    id="default-datepicker" 
    type="text" 
    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" 
    placeholder="Select date"
    readonly
  >
</div>

<script>
const picker = new CustomDatePicker(document.getElementById('default-datepicker'));
</script>
```

### Multiple Date Pickers

```html
<input id="start-date" type="text" placeholder="Start date" readonly>
<input id="end-date" type="text" placeholder="End date" readonly>

<script>
const startPicker = new CustomDatePicker(document.getElementById('start-date'));
const endPicker = new CustomDatePicker(document.getElementById('end-date'));
</script>
```

### Week Picker Mode

```html
<input id="week-picker" type="text" placeholder="Select week" readonly>

<script>
const weekPicker = new CustomDatePicker(document.getElementById('week-picker'), {
  mode: 'week',
  defaultDate: new Date(2025, 7, 9) // August 9, 2025
});
</script>
```

### With Custom Options

```javascript
const picker = new CustomDatePicker(document.getElementById('my-datepicker'), {
  defaultDate: new Date(2024, 0, 1), // January 1, 2024
  format: 'MM/dd/yyyy',
  mode: 'day', // 'day' or 'week'
  minYear: 2020, // Minimum selectable year
  maxYear: 2030  // Maximum selectable year
});
```

### Year Range Restrictions

```javascript
// Birth date picker (1950-2010)
const birthDatePicker = new CustomDatePicker(document.getElementById('birth-date'), {
  defaultDate: new Date(1980, 0, 1),
  minYear: 1950,
  maxYear: 2010
});

// Future event picker (2024-2030)
const futureEventPicker = new CustomDatePicker(document.getElementById('future-event'), {
  defaultDate: new Date(2025, 0, 1),
  minYear: 2024,
  maxYear: 2030
});
```

### Using Static Init Method

```javascript
// Initialize multiple date pickers at once
const pickers = CustomDatePicker.init('.datepicker');

// Or with options
const pickers = CustomDatePicker.init('.datepicker', {
  defaultDate: new Date()
});
```

## Configuration Options

| Option        | Type   | Default                 | Description                                                      |
| ------------- | ------ | ----------------------- | ---------------------------------------------------------------- |
| `defaultDate` | Date   | `new Date(2000, 1, 23)` | Initial date to display                                          |
| `format`      | String | `'MM/dd/yyyy'`          | Date format (currently only MM/dd/yyyy supported)                |
| `mode`        | String | `'day'`                 | Picker mode: 'day' for date selection, 'week' for week selection |
| `minYear`     | Number | `1900`                  | Minimum selectable year (inclusive)                              |
| `maxYear`     | Number | `2100`                  | Maximum selectable year (inclusive)                              |

## Year Range Restrictions

The date picker supports setting minimum and maximum year boundaries to restrict date selection:

- **Navigation Disabled**: Navigation arrows are automatically disabled when reaching year limits
- **Visual Feedback**: Years outside the range are shown as disabled (grayed out)
- **Automatic Bounds**: The initial view year is automatically adjusted to stay within bounds
- **All Views Affected**: Restrictions apply to decade, year, month, and day/week views

### Behavior Details

- **Decade View**: Years outside the range are shown as disabled and non-clickable
- **Year View**: Disabled years cannot be selected
- **Month/Day/Week Views**: Navigation arrows are disabled when at the boundary
- **Initial Display**: If the default date is outside the range, the view adjusts to the nearest valid year

## Events

### datechange

Fired when a date is selected (day mode only).

```javascript
document.getElementById('my-datepicker').addEventListener('datechange', function(e) {
  console.log('Selected date:', e.detail.date);
});
```

### weekchange

Fired when a week is selected (week mode only).

```javascript
document.getElementById('week-picker').addEventListener('weekchange', function(e) {
  console.log('Selected week:', e.detail.week); // {year: 2025, week: 32}
  console.log('Week start:', e.detail.weekStart); // Date object
  console.log('Week end:', e.detail.weekEnd); // Date object
});
```

## Methods

### Constructor

```javascript
new CustomDatePicker(inputElement, options)
```

### Static Methods

```javascript
// Initialize multiple elements
CustomDatePicker.init(selector, options)
```

### Instance Methods

```javascript
// Set a specific date
picker.setDate(new Date(2024, 0, 1));

// Destroy the picker
picker.destroy();
```

## Navigation Flow

The date picker follows a hierarchical navigation pattern:

1. **Day View** (Default) - Shows calendar with days
   - Click header → Month View
   - Use arrows → Navigate months

2. **Month View** - Shows 12 months in a grid
   - Click header → Year View  
   - Click month → Day View
   - Use arrows → Navigate years

3. **Year View** - Shows 10 years in a grid
   - Click header → Decade View
   - Click year → Month View
   - Use arrows → Navigate decades

4. **Decade View** - Shows multiple decades
   - Click year → Year View
   - Use arrows → Navigate decades

## Styling

The date picker uses CSS classes with the `custom-datepicker-` prefix to avoid conflicts:

- `.custom-datepicker-calendar` - Main calendar container
- `.custom-datepicker-header` - Header with navigation
- `.custom-datepicker-nav-button` - Navigation arrows
- `.custom-datepicker-header-title` - Clickable title
- `.custom-datepicker-view-grid` - Grid container
- `.custom-datepicker-grid-item` - Individual grid items
- `.custom-datepicker-grid-item.selected` - Selected item
- `.custom-datepicker-grid-item.today` - Today's date

### Custom Styling Example

```css
/* Customize the calendar appearance */
.custom-datepicker-calendar {
  background: #1a1a1a;
  border: 2px solid #333;
}

.custom-datepicker-grid-item.selected {
  background: #ff6b6b;
}

.custom-datepicker-grid-item:hover {
  background: rgba(255, 107, 107, 0.2);
}
```

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

MIT License - feel free to use in personal and commercial projects.
