<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Custom Select - Auto-Focus Fix Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="./main.css" />
  </head>
  <body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto space-y-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-8">
        Custom Select - Auto-Focus Fix Test
      </h1>

      <div class="bg-green-50 border border-green-200 p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold text-green-800 mb-4">
          ✅ Issue Fixed: Select All No Longer Auto-Selected
        </h2>
        <p class="text-green-700 mb-4">
          <strong>Problem:</strong> When opening multi-select dropdowns, the
          "Select All" option was automatically focused/highlighted instead of
          the first regular option or the first selected option.
        </p>
        <p class="text-green-700 mb-4">
          <strong>Solution:</strong> Updated the focus logic in the
          <code>open()</code> method to account for the DOM index offset created
          by the "Select All" option.
        </p>
      </div>

      <!-- Test Case 1: Multi-select with no pre-selected options -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">
          Test Case 1: No Pre-selected Options
        </h2>
        <p class="text-gray-600 mb-4">
          <strong>Expected behavior:</strong> When opening this dropdown, the
          first regular option ("Option 1") should be focused, NOT the "Select
          All" option.
        </p>

        <label class="block text-sm font-medium text-gray-700 mb-2">
          Multi-select with no pre-selected options
        </label>
        <select
          id="test-case-1"
          multiple
          class="hidden"
          data-select-type="multi"
          data-placeholder="Select options..."
        >
          <option value="opt1">Option 1</option>
          <option value="opt2">Option 2</option>
          <option value="opt3">Option 3</option>
          <option value="opt4">Option 4</option>
          <option value="opt5">Option 5</option>
        </select>
      </div>

      <!-- Test Case 2: Multi-select with pre-selected options -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">
          Test Case 2: With Pre-selected Options
        </h2>
        <p class="text-gray-600 mb-4">
          <strong>Expected behavior:</strong> When opening this dropdown, the
          first selected option ("Option 2") should be focused, NOT the "Select
          All" option.
        </p>

        <label class="block text-sm font-medium text-gray-700 mb-2">
          Multi-select with pre-selected options (Option 2 and Option 4 are
          selected)
        </label>
        <select
          id="test-case-2"
          multiple
          class="hidden"
          data-select-type="multi"
          data-placeholder="Select options..."
        >
          <option value="opt1">Option 1</option>
          <option value="opt2" selected>Option 2 (Pre-selected)</option>
          <option value="opt3">Option 3</option>
          <option value="opt4" selected>Option 4 (Pre-selected)</option>
          <option value="opt5">Option 5</option>
        </select>
      </div>

      <!-- Test Case 3: Multi-select with Select All disabled -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">
          Test Case 3: Select All Disabled
        </h2>
        <p class="text-gray-600 mb-4">
          <strong>Expected behavior:</strong> This dropdown should not have a
          "Select All" option, and the first regular option should be focused
          when opened.
        </p>

        <label class="block text-sm font-medium text-gray-700 mb-2">
          Multi-select with Select All disabled
        </label>
        <select
          id="test-case-3"
          multiple
          class="hidden"
          data-select-type="multi"
          data-placeholder="Select options..."
        >
          <option value="opt1">Option 1</option>
          <option value="opt2">Option 2</option>
          <option value="opt3" selected>Option 3 (Pre-selected)</option>
        </select>
      </div>

      <!-- Instructions -->
      <div class="bg-blue-50 border border-blue-200 p-6 rounded-lg">
        <h3 class="text-lg font-semibold text-blue-800 mb-4">
          Testing Instructions:
        </h3>
        <ol class="text-blue-700 space-y-2">
          <li>
            <strong>1. Test Case 1:</strong> Click to open the first dropdown.
            The pink focus outline should be on "Option 1", not "Select All".
          </li>
          <li>
            <strong>2. Test Case 2:</strong> Click to open the second dropdown.
            The pink focus outline should be on "Option 2" (the first selected
            option), not "Select All".
          </li>
          <li>
            <strong>3. Test Case 3:</strong> Click to open the third dropdown.
            There should be no "Select All" option, and focus should be on
            "Option 3" (the selected option).
          </li>
          <li>
            <strong>4. Keyboard Test:</strong> Use Tab to navigate to any
            dropdown, then press Enter or Space to open it. The focus behavior
            should be the same as clicking.
          </li>
          <li>
            <strong>5. Arrow Keys:</strong> After opening any dropdown, use ↑/↓
            arrow keys to navigate. The focus should move correctly between all
            options including "Select All".
          </li>
        </ol>
      </div>

      <!-- Visual Focus Indicator -->
      <div class="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
        <h4 class="font-semibold text-yellow-800 mb-2">
          Visual Focus Indicator:
        </h4>
        <p class="text-yellow-700 text-sm">
          The focused option will have a <strong>pink outline</strong> around
          it. This makes it easy to see which option is currently focused when
          you open the dropdown or navigate with arrow keys.
        </p>
      </div>
    </div>

    <script src="./js/custom-select.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Initialize the third test case with selectAll disabled
        const testCase3 = document.getElementById("test-case-3");

        // Find the CustomSelect instance for test case 3 and disable selectAll
        setTimeout(() => {
          CustomSelect.instances.forEach((instance) => {
            if (instance.element.id === "test-case-3") {
              instance.options.selectAll = false;
              instance.renderDropdown();
            }
          });
        }, 100);

        // Add some logging to help with testing
        const testCases = ["test-case-1", "test-case-2", "test-case-3"];
        testCases.forEach((id) => {
          const element = document.getElementById(id);
          element.addEventListener("change", (e) => {
            console.log(`${id}: Selected values:`, e.detail.selectedValues);
          });
        });
      });
    </script>
  </body>
</html>
