﻿class CustomSelect { static instances = new Set(); constructor(element, options = {}) { this.element = element; this.isMultiple = element.hasAttribute('multiple') || element.dataset.selectType === 'multi'; this.options = { placeholder: this.isMultiple ? 'Select options' : 'Select an option', searchable: false, selectAll: true, maxHeight: '260px', ...options }; this.selectedValues = this.isMultiple ? [] : null; this.allOptions = []; this.isOpen = false; this.focusedIndex = -1; CustomSelect.instances.add(this); this.init(); } init() { this.parseOptions(); this.createDropdown(); this.attachEventListeners(); this.updateDisplay(); } parseOptions() { if (this.element.tagName === 'SELECT') { const options = Array.from(this.element.querySelectorAll('option')); this.allOptions = options.map(opt => ({ value: opt.value || opt.textContent.trim(), label: opt.textContent.trim(), selected: opt.selected })); } else if (this.element.dataset.options) { this.allOptions = JSON.parse(this.element.dataset.options); } else { this.allOptions = this.options.options || []; } if (this.isMultiple) { this.selectedValues = this.allOptions .filter(opt => opt.selected) .map(opt => opt.value); } else { const selectedOption = this.allOptions.find(opt => opt.selected); this.selectedValues = selectedOption ? selectedOption.value : null; } } createDropdown() { this.element.style.display = 'none'; this.container = document.createElement('div'); this.container.className = `custom-select-container ${this.isMultiple ? 'multi' : 'single'}`; this.trigger = document.createElement('div'); this.trigger.className = 'custom-select-trigger'; this.trigger.setAttribute('role', 'button'); this.trigger.setAttribute('aria-haspopup', 'listbox'); this.trigger.setAttribute('tabindex', '0'); this.triggerText = document.createElement('span'); this.triggerText.className = 'custom-select-text'; this.arrow = document.createElement('span'); this.arrow.className = 'custom-select-arrow'; this.arrow.innerHTML = ` <svg xmlns="http: <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/> </svg> `; this.trigger.appendChild(this.triggerText); this.trigger.appendChild(this.arrow); this.dropdown = document.createElement('div'); this.dropdown.className = 'custom-select-dropdown'; this.dropdown.setAttribute('role', 'listbox'); if (this.isMultiple) { this.dropdown.setAttribute('aria-multiselectable', 'true'); } if (this.isMultiple) { this.chipsContainer = document.createElement('div'); this.chipsContainer.className = 'custom-select-chips'; } this.container.appendChild(this.trigger); this.container.appendChild(this.dropdown); if (this.isMultiple) { this.container.appendChild(this.chipsContainer); } this.element.parentNode.insertBefore(this.container, this.element.nextSibling); this.renderDropdown(); } renderDropdown() { const wrapper = this.dropdown.querySelector('.custom-select-items-wrapper'); const scrollPosition = wrapper ? wrapper.scrollTop : 0; let html = ''; if (this.isMultiple && this.options.selectAll && this.allOptions.length > 1) { const allSelected = this.selectedValues.length === this.allOptions.length; html += ` <div class="custom-select-item custom-select-select-all" role="option" data-value="__select_all__" data-index="-1"> <input type="checkbox" id="select-all-${this.element.id}" class="custom-select-checkbox" ${allSelected ? 'checked' : ''} tabindex="-1"> <label for="select-all-${this.element.id}" class="custom-select-item-text">Select All</label> </div> `; } this.allOptions.forEach((option, index) => { const isSelected = this.isMultiple ? this.selectedValues.includes(option.value) : this.selectedValues === option.value; if (this.isMultiple) { const checkboxId = `${this.element.id}-option-${index}`; html += ` <div class="custom-select-item ${isSelected ? 'selected' : ''}" role="option" data-value="${option.value}" data-index="${index}"> <input type="checkbox" id="${checkboxId}" class="custom-select-checkbox" ${isSelected ? 'checked' : ''} tabindex="-1"> <label for="${checkboxId}" class="custom-select-item-text">${option.label}</label> </div> `; } else { html += ` <div class="custom-select-item ${isSelected ? 'selected' : ''}" role="option" data-value="${option.value}" data-index="${index}" aria-selected="${isSelected}"> <span class="custom-select-item-text">${option.label}</span> </div> `; } }); this.dropdown.innerHTML = `<div class="custom-select-items-wrapper">${html}</div>`; const newWrapper = this.dropdown.querySelector('.custom-select-items-wrapper'); if (newWrapper && scrollPosition > 0) { newWrapper.scrollTop = scrollPosition; } if (this.isOpen && this.focusedIndex >= 0) { this.updateFocus(); } } attachEventListeners() { this.boundToggle = (e) => { e.stopPropagation(); this.toggle(); }; this.boundHandleDropdownClick = (e) => { e.stopPropagation(); const item = e.target.closest('.custom-select-item'); if (item) { this.handleItemClick(item); } }; this.boundHandleKeyDown = (e) => { this.handleKeyDown(e); }; this.boundHandleDocumentClick = () => { this.close(); }; this.boundHandleChipClick = (e) => { if (e.target.classList.contains('custom-select-chip-remove')) { const value = e.target.dataset.value; this.toggleOption(value, false); } }; this.trigger.addEventListener('click', this.boundToggle); this.dropdown.addEventListener('click', this.boundHandleDropdownClick); this.trigger.addEventListener('keydown', this.boundHandleKeyDown); this.dropdown.addEventListener('keydown', this.boundHandleKeyDown); document.addEventListener('click', this.boundHandleDocumentClick); if (this.isMultiple) { this.chipsContainer.addEventListener('click', this.boundHandleChipClick); } } handleKeyDown(e) { switch (e.key) { case 'Enter': case ' ': e.preventDefault(); e.stopPropagation(); if (!this.isOpen) { this.open(); } else if (this.focusedIndex >= 0) { const items = this.dropdown.querySelectorAll('.custom-select-item'); const focusedItem = items[this.focusedIndex]; if (focusedItem) { this.handleItemClick(focusedItem); if (!this.isMultiple) { this.close(); } } } else { this.toggle(); } break; case 'ArrowDown': e.preventDefault(); if (!this.isOpen) { this.open(); } else { this.focusNext(); } break; case 'ArrowUp': e.preventDefault(); if (this.isOpen) { this.focusPrevious(); } break; case 'Escape': e.preventDefault(); this.close(); this.trigger.focus(); break; case 'Tab': if (this.isOpen) { this.close(); } break; } } handleItemClick(item) { const value = item.dataset.value; const items = this.dropdown.querySelectorAll('.custom-select-item'); const clickedIndex = Array.from(items).indexOf(item); if (clickedIndex >= 0) { this.focusedIndex = clickedIndex; this.updateFocus(); } if (value === '__select_all__') { this.toggleSelectAll(); } else if (this.isMultiple) { const checkbox = item.querySelector('.custom-select-checkbox'); this.toggleOption(value, !checkbox.checked); } else { this.selectOption(value); this.close(); } } toggleSelectAll() { const allSelected = this.selectedValues.length === this.allOptions.length; if (allSelected) { this.selectedValues = []; } else { this.selectedValues = this.allOptions.map(opt => opt.value); } this.updateDisplay(); this.renderDropdown(); this.dispatchChangeEvent(); } toggleOption(value, selected) { if (selected && !this.selectedValues.includes(value)) { this.selectedValues.push(value); } else if (!selected) { this.selectedValues = this.selectedValues.filter(v => v !== value); } this.updateDisplay(); this.renderDropdown(); this.dispatchChangeEvent(); if (window.CustomSelectNavigationManager) { window.CustomSelectNavigationManager.enableNavigationPrevention(); } } selectOption(value) { this.selectedValues = value; this.updateDisplay(); this.renderDropdown(); this.dispatchChangeEvent(); if (window.CustomSelectNavigationManager) { window.CustomSelectNavigationManager.enableNavigationPrevention(); } } updateDisplay() { if (this.isMultiple) { const count = this.selectedValues.length; if (count === 0) { this.triggerText.textContent = this.options.placeholder; this.triggerText.classList.add('placeholder'); } else { this.triggerText.textContent = `${count} selected`; this.triggerText.classList.remove('placeholder'); } this.updateChips(); if (this.element.tagName === 'SELECT') { Array.from(this.element.options).forEach(option => { option.selected = this.selectedValues.includes(option.value); }); } } else { const selectedOption = this.allOptions.find(opt => opt.value === this.selectedValues); if (selectedOption) { this.triggerText.textContent = selectedOption.label; this.triggerText.classList.remove('placeholder'); } else { this.triggerText.textContent = this.options.placeholder; this.triggerText.classList.add('placeholder'); } if (this.element.tagName === 'SELECT') { this.element.value = this.selectedValues || ''; Array.from(this.element.options).forEach(option => { option.selected = option.value === this.selectedValues; }); } } } updateChips() { this.chipsContainer.innerHTML = ''; this.selectedValues.forEach(value => { const option = this.allOptions.find(opt => opt.value === value); if (option) { const chip = document.createElement('div'); chip.className = 'custom-select-chip'; chip.innerHTML = ` ${option.label} <span class="custom-select-chip-remove" data-value="${value}">Ã—</span> `; this.chipsContainer.appendChild(chip); } }); } focusNext() { const items = this.dropdown.querySelectorAll('.custom-select-item'); const maxIndex = items.length - 1; if (this.focusedIndex < maxIndex) { this.focusedIndex++; } else { this.focusedIndex = 0; } this.updateFocus(); this.scrollToFocused(); } focusPrevious() { const items = this.dropdown.querySelectorAll('.custom-select-item'); const maxIndex = items.length - 1; if (this.focusedIndex > 0) { this.focusedIndex--; } else { this.focusedIndex = maxIndex; } this.updateFocus(); this.scrollToFocused(); } updateFocus() { const items = this.dropdown.querySelectorAll('.custom-select-item'); items.forEach((item, index) => { item.classList.toggle('focused', index === this.focusedIndex); }); } scrollToFocused() { const items = this.dropdown.querySelectorAll('.custom-select-item'); const focusedItem = items[this.focusedIndex]; if (focusedItem) { const wrapper = this.dropdown.querySelector('.custom-select-items-wrapper'); if (wrapper) { const wrapperRect = wrapper.getBoundingClientRect(); const itemRect = focusedItem.getBoundingClientRect(); const isFullyVisible = itemRect.top >= wrapperRect.top && itemRect.bottom <= wrapperRect.bottom; if (!isFullyVisible) { const itemOffsetTop = focusedItem.offsetTop; const wrapperHeight = wrapper.clientHeight; const itemHeight = focusedItem.offsetHeight; const padding = 20; if (itemRect.top < wrapperRect.top) { wrapper.scrollTop = itemOffsetTop - padding; } else if (itemRect.bottom > wrapperRect.bottom) { wrapper.scrollTop = itemOffsetTop - wrapperHeight + itemHeight + padding; } } } else { focusedItem.scrollIntoView({ block: 'nearest', behavior: 'smooth' }); } } } toggle() { if (this.isOpen) { this.close(); } else { this.open(); } } open() { CustomSelect.closeAllExcept(this); this.isOpen = true; this.dropdown.classList.add('open'); this.trigger.classList.add('open'); this.trigger.setAttribute('aria-expanded', 'true'); if (this.isMultiple) { const firstSelectedIndex = this.allOptions.findIndex(opt => this.selectedValues.includes(opt.value) ); const hasSelectAll = this.options.selectAll && this.allOptions.length > 1; const selectAllOffset = hasSelectAll ? 1 : 0; this.focusedIndex = firstSelectedIndex >= 0 ? firstSelectedIndex + selectAllOffset : selectAllOffset; } else { const selectedIndex = this.allOptions.findIndex(opt => opt.value === this.selectedValues); this.focusedIndex = selectedIndex >= 0 ? selectedIndex : 0; } this.updateFocus(); setTimeout(() => { this.scrollToFocused(); }, 10); } close() { this.isOpen = false; this.dropdown.classList.remove('open'); this.trigger.classList.remove('open'); this.trigger.setAttribute('aria-expanded', 'false'); this.focusedIndex = -1; const items = this.dropdown.querySelectorAll('.custom-select-item'); items.forEach(item => item.classList.remove('focused')); } dispatchChangeEvent() { if (this.isMultiple) { const event = new CustomEvent('change', { detail: { selectedValues: this.selectedValues, selectedOptions: this.allOptions.filter(opt => this.selectedValues.includes(opt.value)) } }); this.element.dispatchEvent(event); } else { const selectedOption = this.allOptions.find(opt => opt.value === this.selectedValues); const event = new CustomEvent('change', { detail: { selectedValue: this.selectedValues, selectedOption: selectedOption } }); this.element.dispatchEvent(event); } } getSelectedValue() { return this.isMultiple ? this.selectedValues : this.selectedValues; } getSelectedValues() { return this.isMultiple ? this.selectedValues : [this.selectedValues].filter(Boolean); } setSelectedValue(value) { if (this.isMultiple) { this.selectedValues = Array.isArray(value) ? value : [value]; } else { this.selectedValues = value; } this.updateDisplay(); this.renderDropdown(); } destroy() { CustomSelect.instances.delete(this); if (this.isOpen) { this.close(); } try { if (this.trigger && this.boundToggle) { this.trigger.removeEventListener('click', this.boundToggle); this.trigger.removeEventListener('keydown', this.boundHandleKeyDown); } if (this.dropdown && this.boundHandleDropdownClick) { this.dropdown.removeEventListener('click', this.boundHandleDropdownClick); this.dropdown.removeEventListener('keydown', this.boundHandleKeyDown); } if (this.chipsContainer && this.isMultiple && this.boundHandleChipClick) { this.chipsContainer.removeEventListener('click', this.boundHandleChipClick); } if (this.boundHandleDocumentClick) { document.removeEventListener('click', this.boundHandleDocumentClick); } } catch (error) { console.warn('CustomSelect: Error removing event listeners during destroy:', error); } try { if (this.container && this.container.parentNode) { this.container.parentNode.removeChild(this.container); } } catch (error) { console.warn('CustomSelect: Error removing DOM elements during destroy:', error); } try { if (this.element && document.contains(this.element)) { this.element.style.display = ''; } } catch (error) { console.warn('CustomSelect: Error restoring original element during destroy:', error); } this.element = null; this.container = null; this.trigger = null; this.dropdown = null; this.chipsContainer = null; this.allOptions = null; this.selectedValues = null; this.boundToggle = null; this.boundHandleDropdownClick = null; this.boundHandleKeyDown = null; this.boundHandleDocumentClick = null; this.boundHandleChipClick = null; } static closeAllExcept(exceptInstance = null) { CustomSelect.instances.forEach(instance => { if (instance !== exceptInstance && instance.isOpen) { instance.close(); } }); } static closeAll() { CustomSelect.closeAllExcept(null); } static cleanupInvalidInstances() { const invalidInstances = []; CustomSelect.instances.forEach(instance => { if (!instance.isValidInstance()) { invalidInstances.push(instance); } }); invalidInstances.forEach(instance => { instance.destroy(); }); return invalidInstances.length; } isValidInstance() { return this.element && document.contains(this.element); } static init(selector, options = {}) { const elements = document.querySelectorAll(selector); const instances = []; elements.forEach(element => { instances.push(new CustomSelect(element, options)); }); return instances.length === 1 ? instances[0] : instances; } static autoInit() { const selects = document.querySelectorAll('select[data-select-type]'); const instances = []; selects.forEach(select => { const placeholder = select.dataset.placeholder; const options = placeholder ? { placeholder } : {}; instances.push(new CustomSelect(select, options)); }); return instances; } static reinitializeAll() { this.cleanupInvalidInstances(); const allSelects = document.querySelectorAll('select[data-select-type]'); allSelects.forEach(select => { const hasInstance = Array.from(this.instances).some(instance => instance.element === select ); if (!hasInstance) { console.log('CustomSelect: Re-initializing select element', select); new CustomSelect(select); } }); } static validateAllInstances() { let validatedCount = 0; let repairedCount = 0; this.instances.forEach(instance => { if (instance.isValidInstance()) { if (!instance.boundToggle) { console.log('CustomSelect: Repairing event listeners for instance', instance); instance.attachEventListeners(); repairedCount++; } validatedCount++; } }); console.log(`CustomSelect: Validated ${validatedCount} instances, repaired ${repairedCount}`); const cleanedCount = this.cleanupInvalidInstances(); if (cleanedCount > 0) { console.log(`CustomSelect: Cleaned up ${cleanedCount} invalid instances during validation`); } } static allowNavigation() { if (window.CustomSelectNavigationManager) { window.CustomSelectNavigationManager.disableNavigationPrevention(); } } static preventNavigation(message = null) { if (window.CustomSelectNavigationManager) { window.CustomSelectNavigationManager.enableNavigationPrevention(message); } } } if (typeof document !== 'undefined') { if (document.readyState === 'loading') { document.addEventListener('DOMContentLoaded', () => CustomSelect.autoInit()); } else { CustomSelect.autoInit(); } class CustomSelectNavigationManager { static isNavigationPrevented = false; static hasUnsavedChanges = false; static preventionMessage = 'You have unsaved changes in your selections. Are you sure you want to leave?'; static enableNavigationPrevention(message = null) { this.isNavigationPrevented = true; this.hasUnsavedChanges = true; if (message) { this.preventionMessage = message; } } static disableNavigationPrevention() { this.isNavigationPrevented = false; this.hasUnsavedChanges = false; } static handleBeforeUnload(event) { if (this.isNavigationPrevented && this.hasUnsavedChanges) { event.preventDefault(); event.returnValue = this.preventionMessage; return this.preventionMessage; } } static handlePageShow(event) { if (event.persisted) { console.log('CustomSelect: Page restored from cache, re-initializing...'); CustomSelect.reinitializeAll(); } } static handleVisibilityChange() { if (document.hidden) { CustomSelect.closeAll(); } else { CustomSelect.validateAllInstances(); } } } window.CustomSelectNavigationManager = CustomSelectNavigationManager; window.addEventListener('beforeunload', (event) => { const result = CustomSelectNavigationManager.handleBeforeUnload(event); if (!CustomSelectNavigationManager.isNavigationPrevented) { CustomSelect.instances.forEach(instance => { instance.destroy(); }); } return result; }); window.addEventListener('pageshow', (event) => { CustomSelectNavigationManager.handlePageShow(event); }); document.addEventListener('visibilitychange', () => { CustomSelectNavigationManager.handleVisibilityChange(); }); window.addEventListener('popstate', () => { console.log('CustomSelect: Browser navigation detected, validating instances...'); CustomSelect.validateAllInstances(); }); setInterval(() => { const cleanedCount = CustomSelect.cleanupInvalidInstances(); if (cleanedCount > 0) { console.log(`CustomSelect: Cleaned up ${cleanedCount} invalid instances`); } }, 30000); }