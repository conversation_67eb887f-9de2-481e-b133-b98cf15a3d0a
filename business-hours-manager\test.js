// Listen for any business hours changes
document.addEventListener('businessHoursChange', function (event) {
    console.log('Hours changed:', event.detail);

    const { businessHours, scannerHours, allData } = event.detail;

    // Update your UI with the new data
    updateBusinessHoursDisplay(businessHours);
    updateScannerHoursDisplay(scannerHours);
});

// Get data programmatically anytime
const currentData = BusinessHoursManager.getAllData();