// Test file for Business Hours Manager - Updated Version
console.log('Business Hours Manager Tests - Updated Version');

// Test 1: Check if BusinessHoursManager is available
if (typeof BusinessHoursManager !== 'undefined') {
    console.log('✓ BusinessHoursManager is available');
} else {
    console.log('✗ BusinessHoursManager is not available');
}

// Listen for any business hours changes
document.addEventListener('businessHoursChange', function (event) {
    console.log('✓ Business hours change event received:', event.detail);

    const { businessHours, scannerHours, allData } = event.detail;

    // Update your UI with the new data
    updateBusinessHoursDisplay(businessHours);
    updateScannerHoursDisplay(scannerHours);
});

document.addEventListener('businessHoursOnlyChange', function (event) {
    console.log('✓ Business hours only change event received:', event.detail);
});

document.addEventListener('scannerHoursOnlyChange', function (event) {
    console.log('✓ Scanner hours only change event received:', event.detail);
});

// Test smart filtering functionality
document.addEventListener('DOMContentLoaded', function () {
    setTimeout(() => {
        console.log('Testing smart filtering and updated features...');

        // Test time configuration
        const manager = BusinessHoursManager.getInstance();
        if (manager) {
            console.log('✓ Time configuration loaded with', manager.timeConfig.options.length, 'options');
            console.log('First option:', manager.timeConfig.options[0]);
            console.log('Last option:', manager.timeConfig.options[manager.timeConfig.options.length - 1]);

            // Test smart filtering
            const startSelect = document.querySelector('[id$="-start"]');
            if (startSelect) {
                console.log('✓ Found start select element');
                startSelect.value = '08:00';
                startSelect.dispatchEvent(new Event('change'));

                const endSelect = document.querySelector('[id$="-end"]');
                if (endSelect) {
                    console.log('✓ End select options filtered. Available options:', endSelect.options.length);
                    console.log('First available end time:', endSelect.options[0]?.text);
                }
            }

            // Test adding extra hours (should be added at the end)
            const addButton = document.querySelector('.add-hours-btn.plus-icon');
            if (addButton) {
                console.log('✓ Found add hours button - testing extra hours placement');
                addButton.click();

                setTimeout(() => {
                    const additionalSlots = document.querySelectorAll('.additional-time-slot');
                    console.log('✓ Additional time slots count:', additionalSlots.length);
                    if (additionalSlots.length > 0) {
                        console.log('✓ Latest extra hours added at the end');
                    }
                }, 500);
            }
        }
    }, 1000);
});

// Get data programmatically anytime
const currentData = BusinessHoursManager.getAllData();

// Helper functions for UI updates
function updateBusinessHoursDisplay(businessHours) {
    console.log('Updating business hours display:', businessHours);
}

function updateScannerHoursDisplay(scannerHours) {
    console.log('Updating scanner hours display:', scannerHours);
}

console.log('All tests initialized. Check the console for results.');