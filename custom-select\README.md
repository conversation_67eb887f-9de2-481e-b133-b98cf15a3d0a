# Custom Select Components

A lightweight, accessible custom select component library supporting both single and multi-select dropdowns with full keyboard navigation support.

## Features

### Core Features
- **Unified Component**: Single file for both single and multi-select functionality
- **Auto-initialization**: Automatically initializes via data attributes - no manual JavaScript needed
- **Full Keyboard Navigation**: Arrow Up/Down, Enter/Space, Escape, Tab
- **Accessible**: Full ARIA attributes and keyboard support for screen readers
- **Customizable**: Easy styling and configuration options
- **No Dependencies**: Pure vanilla JavaScript - no jQuery or other libraries required
- **Reusable**: Use data attributes to initialize multiple selects without writing code
- **Visual Feedback**: Focus indicators, hover states, and smooth animations

### ✨ Enhanced User Experience Features
- **🎯 Global Dropdown Management**: Only one dropdown open at a time prevents confusion
- **📍 Auto-scroll to Selected**: Selected options are immediately visible when opening dropdowns
- **🧠 Memory Efficient**: Automatic cleanup prevents memory leaks and handles DOM changes
- **🔄 Consistent Behavior**: Works the same way across all dropdown instances
- **⚡ Performance Optimized**: Efficient event handling and DOM management

## Quick Start

### 1. Include the Script

```html
<script src="./js/custom-select.js"></script>
```

### 2. Add Data Attributes to Your Select Elements

**Multi-Select:**
```html
<select multiple data-select-type="multi" data-placeholder="Select options...">
  <option value="1">Option 1</option>
  <option value="2" selected>Option 2</option>
  <option value="3">Option 3</option>
</select>
```

**Single-Select:**
```html
<select data-select-type="single" data-placeholder="Choose an option">
  <option value="">Select...</option>
  <option value="1">Option 1</option>
  <option value="2">Option 2</option>
</select>
```

That's it! The component will automatically initialize on page load.

## Enhanced User Experience

### 🎯 Global Dropdown Management
Only one dropdown can be open at a time across your entire page. When you open a new dropdown, any previously opened dropdown automatically closes. This prevents user confusion and provides a cleaner, more focused experience.

### 📍 Auto-scroll to Selected Options
When opening a dropdown, the component automatically scrolls to show your selected options:
- **Single-select**: Scrolls to the currently selected option
- **Multi-select**: Scrolls to the first selected option
- **No manual scrolling needed**: Users can immediately see their selections without hunting through long lists

### 🧠 Memory Efficient Design
- **Automatic cleanup**: Removes event listeners and DOM references when dropdowns are destroyed
- **Invalid instance detection**: Automatically cleans up dropdowns whose DOM elements have been removed
- **Page lifecycle handling**: Closes all dropdowns when the page becomes hidden
- **Periodic maintenance**: Runs background cleanup every 30 seconds to prevent memory leaks

### 🔄 Consistent Behavior
All dropdown instances behave identically, providing a predictable and reliable user experience across your application.

### 🛡️ Browser Navigation Prevention
Automatically prevents accidental page navigation when users have unsaved dropdown changes:
- **Automatic activation**: Enabled when users change any dropdown selection
- **Browser warnings**: Shows confirmation dialogs for refresh, tab close, or navigation attempts
- **Smart recovery**: Dropdowns continue working perfectly if users cancel navigation
- **Manual control**: Programmatic enable/disable functionality

## Usage

### Auto-Initialization (Recommended)

Simply add the `data-select-type` attribute to your select elements:

```html
<!-- Multi-select -->
<select id="skills" multiple data-select-type="multi" data-placeholder="Select your skills...">
  <option value="js">JavaScript</option>
  <option value="python">Python</option>
  <option value="react">React</option>
</select>

<!-- Single-select -->
<select id="department" data-select-type="single" data-placeholder="Select Department">
  <option value="">Select...</option>
  <option value="hr">Human Resources</option>
  <option value="it">IT</option>
</select>
```

### Manual Initialization

```javascript
// Initialize a single element
const mySelect = new CustomSelect(document.getElementById('my-select'), {
  placeholder: 'Custom placeholder',
  selectAll: true
});

// Initialize multiple elements
const selects = CustomSelect.init('select.custom', {
  placeholder: 'Select options...'
});
```

## Keyboard Navigation

### All Dropdowns
- **Enter** or **Space**: Open/close dropdown, or select focused item
- **Arrow Down**: Open dropdown (if closed) or move to next item
- **Arrow Up**: Move to previous item
- **Escape**: Close dropdown and return focus to trigger
- **Tab**: Close dropdown and move to next element

### Multi-Select Specific
- Items remain selected when navigating
- **Enter**/**Space** toggles selection of focused item
- Click × on chips to remove selections
- "Select All" checkbox available

### Single-Select Specific
- **Enter**/**Space** selects item and closes dropdown
- Visual focus indicator on navigation
- Radio button shows selected item

## Configuration Options

```javascript
{
  placeholder: 'Select options',  // Placeholder text
  searchable: false,              // Enable search (future)
  selectAll: true,                // Show "Select All" (multi only)
  maxHeight: '260px'              // Max dropdown height
}
```

### Data Attributes

- `data-select-type`: "single" or "multi"
- `data-placeholder`: Custom placeholder text

## API Methods

### Instance Methods

```javascript
const select = new CustomSelect(element);

// Get selected value(s)
select.getSelectedValue();      // Single value or array
select.getSelectedValues();     // Always returns array

// Set selected value(s)
select.setSelectedValue('value');
select.setSelectedValue(['val1', 'val2']);

// Control dropdown
select.open();
select.close();
select.toggle();

// Cleanup
select.destroy();
```

### Static Methods

```javascript
// Auto-initialize all selects with data-select-type
CustomSelect.autoInit();

// Initialize specific selects
CustomSelect.init('select.my-class', options);

// Global dropdown management
CustomSelect.closeAll();                    // Close all open dropdowns
CustomSelect.closeAllExcept(instance);      // Close all except specified instance

// Memory management
CustomSelect.cleanupInvalidInstances();     // Remove instances with deleted DOM elements

// Navigation prevention
CustomSelect.preventNavigation();           // Enable navigation prevention
CustomSelect.preventNavigation('Custom message'); // Enable with custom warning message
CustomSelect.allowNavigation();             // Disable navigation prevention
```


## Events

Listen to change events on the original select element:

```javascript
const selectElement = document.getElementById('my-select');

selectElement.addEventListener('change', (e) => {
  // Multi-select
  console.log(e.detail.selectedValues);   // Array of values
  console.log(e.detail.selectedOptions);  // Array of option objects

  // Single-select
  console.log(e.detail.selectedValue);    // Single value
  console.log(e.detail.selectedOption);   // Single option object
});
```

## Migration from Old Code

### Before (Old Approach)
```javascript
// Required separate files and manual initialization
<script src="./js/multi-select.js"></script>
<script src="./js/single-select.js"></script>

const multiSelect1 = new MultiSelect(document.getElementById("multi-select-1"));
const singleSelect1 = new SingleSelect(document.getElementById("single-select-1"));
```

### After (New Approach)
```html
<!-- Single unified file -->
<script src="./js/custom-select.js"></script>

<!-- Just add data attributes - auto-initializes -->
<select id="multi-select-1" multiple data-select-type="multi">...</select>
<select id="single-select-1" data-select-type="single">...</select>
```

No JavaScript initialization needed!

## Bug Fixes

### Fixed Issues:
- Multi-select up/down arrow navigation now works
- Escape key properly closes dropdown
- All keyboard controls work correctly
- Focus management improved
- Eliminated code duplication
- Focus wraps around when navigating
- Smooth scrolling to focused items

## Styling

Customize by modifying CSS in `custom-select.js` or override these classes:

- `.custom-select-container` - Main wrapper
- `.custom-select-trigger` - Clickable trigger
- `.custom-select-dropdown` - Dropdown menu
- `.custom-select-item` - Option items
- `.custom-select-item.focused` - Focused item
- `.custom-select-item.selected` - Selected item
- `.custom-select-chip` - Chip (multi-select)

## File Structure

```
custom-select/
├── index.html              # Demo page
├── js/
│   ├── custom-select.js   # Unified component (USE THIS)
│   ├── multi-select.js    # Legacy - deprecated
│   └── single-select.js   # Legacy - deprecated
└── README.md              # This file
```

## Browser Support

- Chrome/Edge (latest)
- Firefox (latest)
- Safari (latest)
- Modern browsers with ES6 support

## Examples

See `index.html` for complete examples including:
- Basic multi-select with pre-selected options
- Basic single-select
- Form integration
- Event handling
- Programmatic value setting

## License

MIT License - feel free to use in your projects!
