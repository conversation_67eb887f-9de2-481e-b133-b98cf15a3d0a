# Business Hours Manager - Latest Update v2.0

## Overview

The BusinessHoursManager has been significantly enhanced with smart time selection, intelligent filtering, and improved user experience. This update includes custom events similar to the custom-select.js component, allowing external code to listen for changes and access business hours data in real-time.

## 🆕 Latest Features (v2.0)

### ⚡ Smart Time Selection
- **Intelligent Filtering**: End time options automatically filter based on start time selection
- **Unique Options**: Eliminates duplicate time options for cleaner selection
- **Proper Time Display**: Hours displayed in proper 12-hour format with AM/PM
- **Smart Defaults**: Time range from 06:00 AM to 11:30 PM in 30-minute intervals

### 📍 Enhanced Extra Hours Management
- **Latest Hours at End**: New extra hours are added at the end of the list (not at the top)
- **Better Organization**: More logical ordering of time slots
- **Improved Animation**: Smooth animations when adding/removing time slots

### 🎯 Custom Events System
- **`businessHoursChange`** - Dispatched on any change (business or scanner hours)
- **`businessHoursOnlyChange`** - Dispatched only when business hours change
- **`scannerHoursOnlyChange`** - Dispatched only when scanner hours change

### 🔧 Static Methods for Data Access
- `BusinessHoursManager.getBusinessHours()` - Get business hours data
- `BusinessHoursManager.getScannerHours()` - Get scanner hours data
- `BusinessHoursManager.getAllData()` - Get complete data structure
- `BusinessHoursManager.getInstance()` - Get current instance

### 📊 Rich Event Data
Each event includes:
- Structured hours data for each day
- Validation status
- Timestamp
- Complete data context

## Quick Start

### 1. Smart Time Selection Example
```javascript
// The component automatically handles smart filtering
// When user selects 08:00 AM as start time:
// - End time options will show: 08:30 AM, 09:00 AM, 09:30 AM, ... 11:30 PM
// - Invalid options (06:00 AM - 08:00 AM) are automatically hidden
// - No need for manual validation of time combinations

// Example scenario:
// Start Hours: 06:00 AM - 11:00 PM (all available)
// End Hours: Filtered based on start selection
// If start = 08:00 AM, end shows only 08:30 AM onwards
```

### 2. Listen for Changes
```javascript
document.addEventListener('businessHoursChange', function(event) {
  console.log('Hours changed:', event.detail);

  // Access the data
  const { businessHours, scannerHours, allData } = event.detail;

  // Update your UI
  updateHoursDisplay(businessHours);
});
```

### 3. Get Data Programmatically
```javascript
// Get current data anytime
const businessHours = BusinessHoursManager.getBusinessHours();
const scannerHours = BusinessHoursManager.getScannerHours();
const allData = BusinessHoursManager.getAllData();
```

### 4. Extra Hours Management
```javascript
// Extra hours are now added at the END of the list
// This provides better logical ordering:
// 1. Main business hours (e.g., 09:00 AM - 05:00 PM)
// 2. Additional hours (e.g., 07:00 PM - 09:00 PM) <- Added at end
// 3. More additional hours (e.g., 10:00 PM - 11:00 PM) <- Added at end
```

### 3. Real-time Rendering
```javascript
function renderHours(hoursData) {
  hoursData.forEach(day => {
    if (day.is_closed) {
      console.log(`${day.day}: Closed`);
    } else {
      day.hours.forEach(slot => {
        console.log(`${day.day}: ${slot.open_time} - ${slot.close_time}`);
      });
    }
  });
}

document.addEventListener('businessHoursChange', function(event) {
  renderHours(event.detail.businessHours);
});
```

## Files

- **`business-hours-manager.js`** - Main component with custom events
- **`example-usage.html`** - Complete working example with event logging
- **`EVENTS_DOCUMENTATION.md`** - Detailed documentation and examples
- **`style.css`** - Styling for the component

## Data Structure

```javascript
// Each day follows this structure:
{
  day: "Monday",           // Day name
  is_closed: false,        // Whether closed
  is_open_24: false,       // Whether 24/7 (future feature)
  hours: [                 // Time slots array
    {
      open_time: "09:00",  // Opening time
      close_time: "17:00"  // Closing time
    }
  ]
}
```

## Event Triggers

Events are automatically dispatched when:
- ✅ Day toggles are changed (on/off)
- ✅ Time values are modified
- ✅ Time slots are added/removed
- ✅ "Same as Business Hours" is toggled

## Integration Examples

### Save to Server
```javascript
document.addEventListener('businessHoursChange', function(event) {
  fetch('/api/save-hours', {
    method: 'POST',
    body: JSON.stringify(event.detail.allData)
  });
});
```

### Form Validation
```javascript
document.addEventListener('businessHoursChange', function(event) {
  const isValid = event.detail.allData.validation.businessValid;
  document.getElementById('submit-btn').disabled = !isValid;
});
```

### Local Storage
```javascript
document.addEventListener('businessHoursChange', function(event) {
  localStorage.setItem('hours', JSON.stringify(event.detail.allData));
});
```

## Browser Support

Works in all modern browsers that support:
- CustomEvent API
- ES6 features
- DOM manipulation

## Demo

Open `example-usage.html` in your browser to see the custom events in action with:
- Real-time event logging
- Data display updates
- Interactive controls
- Complete integration example

## Migration from Previous Version

The component is backward compatible. Existing code will continue to work, and you can gradually add event listeners to take advantage of the new features.

## Similar Implementation

This event system follows the same pattern as `custom-select.js`, providing consistency across components in your application.

## Changelog

### v2.0.0 (Latest Update)
- ✨ **Smart Time Selection**: Automatic filtering of end time options based on start time
- ✨ **Proper Time Format**: 12-hour format with AM/PM display (06:00 AM - 11:30 PM)
- ✨ **Unique Options**: Eliminated duplicate time options for cleaner selection
- ✨ **Extra Hours at End**: New time slots are added at the end for better organization
- ✨ **Enhanced Time Range**: Extended range with 30-minute intervals
- 🐛 **Fixed Time Display**: Improved formatting and consistency
- 🐛 **Better UX**: More logical time slot ordering and placement

### v1.1.0 (Previous)
- 🎯 Custom Events System
- 🔧 Static Methods for Data Access
- 📊 Rich Event Data
- ✅ Real-time change detection

### v1.0.0 (Initial)
- Basic business hours management
- Scanner hours functionality
- Toggle system
- Responsive design
